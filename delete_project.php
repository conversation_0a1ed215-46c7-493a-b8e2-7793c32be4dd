<?php
require_once 'config/database.php';

// Check if project ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: view_projects.php?error=invalid_id');
    exit;
}

$project_id = (int)$_GET['id'];
$confirm = $_GET['confirm'] ?? '';

try {
    $pdo = getDatabaseConnection();
    
    // Get project details first
    $stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$project_id]);
    $project = $stmt->fetch();
    
    if (!$project) {
        header('Location: view_projects.php?error=project_not_found');
        exit;
    }
    
    // If confirmation is provided, proceed with deletion
    if ($confirm === 'yes') {
        // Start transaction
        $pdo->beginTransaction();
        
        try {
            // Delete from all related tables in correct order to avoid foreign key issues
            // The CASCADE should handle this, but let's be explicit to avoid issues

            // Delete all child records first (though CASCADE should handle this)
            $tables_to_clean = [
                'footprint', 'flooring', 'beams', 'columns', 'staircases',
                'slabs', 'openings', 'windows', 'doors', 'walls', 'floors'
            ];

            foreach ($tables_to_clean as $table) {
                $stmt = $pdo->prepare("DELETE FROM {$table} WHERE project_id = ?");
                $stmt->execute([$project_id]);
            }

            // Finally delete the project
            $stmt = $pdo->prepare("DELETE FROM projects WHERE id = ?");
            $stmt->execute([$project_id]);
            
            // Delete the DXF file from filesystem
            $dxf_file_path = __DIR__ . '/uploads/dxf_files/' . $project['dxf_filename'];
            if (file_exists($dxf_file_path)) {
                unlink($dxf_file_path);
            }
            
            // Delete log file if exists
            $log_file_path = __DIR__ . '/logs/dxf_processing_' . $project_id . '.log';
            if (file_exists($log_file_path)) {
                unlink($log_file_path);
            }
            
            // Commit transaction
            $pdo->commit();
            
            header('Location: view_projects.php?success=project_deleted');
            exit;
            
        } catch (Exception $e) {
            // Rollback transaction
            $pdo->rollback();
            throw $e;
        }
    }
    
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Project - SmartEstimate 3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #dc3545;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            border: 1px solid #ffeaa7;
        }
        
        .warning h3 {
            margin-bottom: 10px;
            color: #dc3545;
        }
        
        .project-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .project-info h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .project-info table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .project-info th,
        .project-info td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .project-info th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            cursor: pointer;
            transition: background-color 0.3s;
            display: inline-block;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Delete Project</h1>
        
        <?php if (isset($error_message)): ?>
            <div class="error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($project)): ?>
            <div class="warning">
                <h3>⚠️ Warning: This action cannot be undone!</h3>
                <p>You are about to permanently delete this project and all associated data, including:</p>
                <ul style="margin-top: 10px; margin-left: 20px;">
                    <li>Project information</li>
                    <li>All extracted DXF data (floors, walls, doors, windows, etc.)</li>
                    <li>Uploaded DXF file</li>
                    <li>Processing logs</li>
                </ul>
            </div>
            
            <div class="project-info">
                <h3>Project Information</h3>
                <table>
                    <tr><th>Project ID</th><td><?php echo htmlspecialchars($project['id']); ?></td></tr>
                    <tr><th>R.S. No</th><td><?php echo htmlspecialchars($project['rs_no']); ?></td></tr>
                    <tr><th>Ward No</th><td><?php echo htmlspecialchars($project['ward_no']); ?></td></tr>
                    <tr><th>Panchayath/Municipality</th><td><?php echo htmlspecialchars($project['panchayath_municipality']); ?></td></tr>
                    <tr><th>Client Name</th><td><?php echo htmlspecialchars($project['client_name']); ?></td></tr>
                    <tr><th>DXF File</th><td><?php echo htmlspecialchars($project['dxf_filename']); ?></td></tr>
                    <tr><th>Status</th><td><?php echo htmlspecialchars($project['processing_status']); ?></td></tr>
                    <tr><th>Created</th><td><?php echo date('Y-m-d H:i:s', strtotime($project['created_at'])); ?></td></tr>
                </table>
            </div>
            
            <div class="action-buttons">
                <a href="delete_project.php?id=<?php echo $project_id; ?>&confirm=yes"
                   class="btn btn-danger">
                    Yes, Delete Project
                </a>
                <a href="view_projects.php" class="btn btn-secondary">Cancel</a>
            </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="view_projects.php">← Back to Projects</a>
            <a href="index.php">Dashboard</a>
        </div>
    </div>
</body>
</html>
