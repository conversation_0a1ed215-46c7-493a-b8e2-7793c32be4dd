#!/usr/bin/env python3
"""
Test DXF File Generator for SmartEstimate 3.0
Creates a sample DXF file with the expected layer structure for testing
"""

import ezdxf
from ezdxf.math import Vec2

def create_test_dxf():
    """Create a test DXF file with sample architectural elements"""
    
    # Create new DXF document
    doc = ezdxf.new('R2010')
    msp = doc.modelspace()
    
    # Create layers
    layers = [
        'GROUND_FLOOR', 'FIRST_FLOOR',
        'WALLS_10CM', 'WALLS_20CM', 'WALLS_23CM',
        'DOOR_D', 'DOOR_D1', 'WINDOW_W1', 'WINDOW_W2',
        'OPENINGS', 'MAIN_SLAB_TOTAL', 'STAIRCASE',
        'RCC_COLUMN', 'RCC_BEAM', 'FLOORING_MAIN', 'FOOTPRINT'
    ]
    
    for layer_name in layers:
        doc.layers.new(name=layer_name)
    
    # Ground Floor boundary (20m x 15m)
    ground_floor_points = [
        (0, 0), (20, 0), (20, 15), (0, 15), (0, 0)
    ]
    msp.add_lwpolyline(ground_floor_points, dxfattribs={'layer': 'GROUND_FLOOR'})
    
    # First Floor boundary (slightly smaller)
    first_floor_points = [
        (1, 1), (19, 1), (19, 14), (1, 14), (1, 1)
    ]
    msp.add_lwpolyline(first_floor_points, dxfattribs={'layer': 'FIRST_FLOOR'})
    
    # Walls (10cm thickness)
    wall_10cm_rects = [
        [(2, 2), (18, 2), (18, 2.1), (2, 2.1), (2, 2)],  # Bottom wall
        [(2, 13), (18, 13), (18, 13.1), (2, 13.1), (2, 13)],  # Top wall
        [(2, 2), (2.1, 2), (2.1, 13), (2, 13), (2, 2)],  # Left wall
        [(17.9, 2), (18, 2), (18, 13), (17.9, 13), (17.9, 2)]  # Right wall
    ]
    
    for rect in wall_10cm_rects:
        msp.add_lwpolyline(rect, dxfattribs={'layer': 'WALLS_10CM'})
    
    # Walls (20cm thickness)
    wall_20cm_rects = [
        [(8, 6), (12, 6), (12, 6.2), (8, 6.2), (8, 6)],  # Internal wall
        [(8, 9), (12, 9), (12, 9.2), (8, 9.2), (8, 9)]   # Another internal wall
    ]
    
    for rect in wall_20cm_rects:
        msp.add_lwpolyline(rect, dxfattribs={'layer': 'WALLS_20CM'})
    
    # Doors
    door_rects = [
        [(9, 2), (11, 2), (11, 2.1), (9, 2.1), (9, 2)],    # Main door
        [(5, 6), (7, 6), (7, 6.2), (5, 6.2), (5, 6)]       # Internal door
    ]
    
    for i, rect in enumerate(door_rects):
        layer = 'DOOR_D' if i == 0 else 'DOOR_D1'
        msp.add_lwpolyline(rect, dxfattribs={'layer': layer})
    
    # Windows
    window_rects = [
        [(4, 2), (6, 2), (6, 2.1), (4, 2.1), (4, 2)],      # Window 1
        [(14, 2), (16, 2), (16, 2.1), (14, 2.1), (14, 2)], # Window 2
        [(2, 5), (2.1, 5), (2.1, 7), (2, 7), (2, 5)],      # Side window 1
        [(17.9, 8), (18, 8), (18, 10), (17.9, 10), (17.9, 8)] # Side window 2
    ]
    
    for i, rect in enumerate(window_rects):
        layer = 'WINDOW_W1' if i < 2 else 'WINDOW_W2'
        msp.add_lwpolyline(rect, dxfattribs={'layer': layer})
    
    # Openings
    opening_rects = [
        [(6, 9), (8, 9), (8, 9.2), (6, 9.2), (6, 9)]  # Opening
    ]
    
    for rect in opening_rects:
        msp.add_lwpolyline(rect, dxfattribs={'layer': 'OPENINGS'})
    
    # Main slab (covers most of the floor)
    slab_points = [
        (3, 3), (17, 3), (17, 12), (3, 12), (3, 3)
    ]
    msp.add_lwpolyline(slab_points, dxfattribs={'layer': 'MAIN_SLAB_TOTAL'})
    
    # Staircase
    stair_points = [
        (15, 4), (17, 4), (17, 8), (15, 8), (15, 4)
    ]
    msp.add_lwpolyline(stair_points, dxfattribs={'layer': 'STAIRCASE'})
    
    # Columns (30cm x 30cm)
    column_positions = [(4, 4), (16, 4), (4, 11), (16, 11)]
    
    for x, y in column_positions:
        column_rect = [
            (x, y), (x + 0.3, y), (x + 0.3, y + 0.3), (x, y + 0.3), (x, y)
        ]
        msp.add_lwpolyline(column_rect, dxfattribs={'layer': 'RCC_COLUMN'})
    
    # Beams (connecting columns)
    beam_rects = [
        [(4, 4.15), (16, 4.15), (16, 4.45), (4, 4.45), (4, 4.15)],  # Bottom beam
        [(4, 10.85), (16, 10.85), (16, 11.15), (4, 11.15), (4, 10.85)]  # Top beam
    ]
    
    for rect in beam_rects:
        msp.add_lwpolyline(rect, dxfattribs={'layer': 'RCC_BEAM'})
    
    # Flooring areas
    flooring_rects = [
        [(5, 5), (15, 5), (15, 10), (5, 10), (5, 5)]  # Main flooring area
    ]
    
    for rect in flooring_rects:
        msp.add_lwpolyline(rect, dxfattribs={'layer': 'FLOORING_MAIN'})
    
    # Building footprint
    footprint_points = [
        (0, 0), (20, 0), (20, 15), (0, 15), (0, 0)
    ]
    msp.add_lwpolyline(footprint_points, dxfattribs={'layer': 'FOOTPRINT'})
    
    # Save the DXF file
    filename = 'test_building.dxf'
    doc.saveas(filename)
    print(f"Test DXF file created: {filename}")
    
    # Print summary
    print("\nTest DXF file contains:")
    print("- 2 floors (Ground and First)")
    print("- Walls with different thicknesses")
    print("- 2 doors and 4 windows")
    print("- 1 opening")
    print("- Main slab area")
    print("- 1 staircase")
    print("- 4 columns (30cm x 30cm)")
    print("- 2 beams")
    print("- Main flooring area")
    print("- Building footprint")
    print(f"\nBuilding dimensions: 20m x 15m")
    print(f"Total area: 300 sq.m")

if __name__ == "__main__":
    try:
        create_test_dxf()
    except Exception as e:
        print(f"Error creating test DXF: {e}")
        print("Make sure ezdxf is installed: pip install ezdxf")
