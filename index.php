<?php
// SmartEstimate 3.0 - Main Landing Page
require_once 'config/database.php';

// Get basic statistics
try {
    $pdo = getDatabaseConnection();
    
    // Get project counts by status
    $stats_query = "
        SELECT 
            processing_status,
            COUNT(*) as count
        FROM projects 
        GROUP BY processing_status
    ";
    $stmt = $pdo->query($stats_query);
    $status_stats = $stmt->fetchAll();
    
    // Get total projects
    $total_query = "SELECT COUNT(*) as total FROM projects";
    $stmt = $pdo->query($total_query);
    $total_projects = $stmt->fetch()['total'];
    
    // Get recent projects
    $recent_query = "
        SELECT id, rs_no, client_name, processing_status, upload_date
        FROM projects 
        ORDER BY upload_date DESC 
        LIMIT 5
    ";
    $stmt = $pdo->query($recent_query);
    $recent_projects = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = "Database error: " . $e->getMessage();
}

function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge badge-warning">Pending</span>',
        'processing' => '<span class="badge badge-info">Processing</span>',
        'completed' => '<span class="badge badge-success">Completed</span>',
        'error' => '<span class="badge badge-danger">Error</span>'
    ];
    return $badges[$status] ?? '<span class="badge badge-secondary">Unknown</span>';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartEstimate 3.0 - DXF Processing System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
        }
        
        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(108,117,125,0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }
        
        .recent-projects {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .projects-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .projects-table th,
        .projects-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .projects-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .projects-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .badge-warning { background-color: #ffc107; color: #212529; }
        .badge-info { background-color: #17a2b8; color: white; }
        .badge-success { background-color: #28a745; color: white; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-secondary { background-color: #6c757d; color: white; }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }
        
        .feature {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            color: white;
            text-align: center;
        }
        
        .feature h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .feature p {
            opacity: 0.9;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SmartEstimate 3.0</h1>
            <p>Advanced DXF Processing System for Construction Estimation</p>
        </div>
        
        <?php if (isset($error_message)): ?>
            <div class="error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <div class="dashboard">
            <!-- Statistics Card -->
            <div class="card">
                <h3>Project Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $total_projects ?? 0; ?></div>
                        <div class="stat-label">Total Projects</div>
                    </div>
                    <?php if (isset($status_stats)): ?>
                        <?php foreach ($status_stats as $stat): ?>
                            <div class="stat-item">
                                <div class="stat-number"><?php echo $stat['count']; ?></div>
                                <div class="stat-label"><?php echo ucfirst($stat['processing_status']); ?></div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Quick Actions Card -->
            <div class="card">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="add_project.php" class="btn">Add New Project</a>
                    <a href="view_projects.php" class="btn btn-secondary">View All Projects</a>
                </div>
                <div class="action-buttons" style="margin-top: 15px;">
                    <a href="project_settings.php" class="btn" style="background: linear-gradient(135deg, #28a745, #20c997); font-size: 14px;">⚙️ Project Settings</a>
                    <a href="dev_clear_all.php" class="btn btn-danger" style="font-size: 14px;">🚨 DEV: Clear All</a>
                </div>
            </div>
        </div>
        
        <!-- Recent Projects -->
        <?php if (isset($recent_projects) && !empty($recent_projects)): ?>
            <div class="recent-projects">
                <h3>Recent Projects</h3>
                <table class="projects-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>R.S. No</th>
                            <th>Client Name</th>
                            <th>Status</th>
                            <th>Upload Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_projects as $project): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($project['id']); ?></td>
                                <td><?php echo htmlspecialchars($project['rs_no']); ?></td>
                                <td><?php echo htmlspecialchars($project['client_name']); ?></td>
                                <td><?php echo getStatusBadge($project['processing_status']); ?></td>
                                <td><?php echo date('Y-m-d H:i', strtotime($project['upload_date'])); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <!-- Features -->
        <div class="features">
            <div class="feature">
                <h4>🏗️ DXF Processing</h4>
                <p>Advanced parsing of DXF files with layer-based data extraction</p>
            </div>
            <div class="feature">
                <h4>📊 Data Analytics</h4>
                <p>Comprehensive analysis of architectural elements and measurements</p>
            </div>
            <div class="feature">
                <h4>🗄️ Database Storage</h4>
                <p>Structured storage with relational database design</p>
            </div>
            <div class="feature">
                <h4>🔍 Advanced Filtering</h4>
                <p>Search and filter projects by multiple criteria</p>
            </div>
        </div>
    </div>
</body>
</html>
