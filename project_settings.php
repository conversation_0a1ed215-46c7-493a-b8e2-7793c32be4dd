<?php
require_once 'config/database.php';

// Initialize variables
$errors = [];
$success_message = '';
$selected_project_id = $_GET['project_id'] ?? $_POST['project_id'] ?? '';

// Default values for settings
$default_settings = [
    'excavation_depth' => 1.00,
    'beam_depth' => 0.20,
    'slab_thickness' => 0.13,
    'door_height' => 2.10,
    'cc_thickness' => 0.05,
    'reinforcement' => 110.00,
    'wall_tile_height' => 2.10,
    'electrical_works' => 100000.00,
    'plumbing_work' => 100000.00,
    'unforeseen_items' => 10000.00,
    'wall_height' => 3.00,
    'basement_width' => 0.30,
    'basement_height' => 0.60,
    'window_kw3_height' => 1.10,
    'window_v1_height' => 0.60,
    'window_w1_height' => 1.50,
    'window_w2_height' => 1.50,
    'window_w3_height' => 1.50,
    'window_w4_height' => 1.50
];

$current_settings = $default_settings;

try {
    $pdo = getDatabaseConnection();
    
    // Get all projects for dropdown
    $projects_stmt = $pdo->query("
        SELECT id, rs_no, client_name, processing_status 
        FROM projects 
        ORDER BY created_at DESC
    ");
    $projects = $projects_stmt->fetchAll();
    
    // If project_id is provided, load existing settings
    if ($selected_project_id) {
        // Validate project exists
        $project_stmt = $pdo->prepare("SELECT id, rs_no, client_name FROM projects WHERE id = ?");
        $project_stmt->execute([$selected_project_id]);
        $project = $project_stmt->fetch();
        
        if (!$project) {
            $errors[] = "Project not found";
            $selected_project_id = '';
        } else {
            // Load existing settings if they exist
            $settings_stmt = $pdo->prepare("SELECT * FROM project_settings WHERE project_id = ?");
            $settings_stmt->execute([$selected_project_id]);
            $existing_settings = $settings_stmt->fetch();
            
            if ($existing_settings) {
                // Use existing settings
                foreach ($default_settings as $key => $default_value) {
                    if (isset($existing_settings[$key])) {
                        $current_settings[$key] = $existing_settings[$key];
                    }
                }
            }
        }
    }
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['project_id'])) {
        $selected_project_id = $_POST['project_id'];
        
        // Validate project exists
        $project_stmt = $pdo->prepare("SELECT id FROM projects WHERE id = ?");
        $project_stmt->execute([$selected_project_id]);
        if (!$project_stmt->fetch()) {
            $errors[] = "Invalid project selected";
        }
        
        // Validate and sanitize input values
        $input_settings = [];
        
        // Numeric validations
        $numeric_fields = [
            'excavation_depth' => ['min' => 0, 'max' => 10],
            'beam_depth' => ['min' => 0, 'max' => 2],
            'slab_thickness' => ['min' => 0, 'max' => 1],
            'door_height' => ['min' => 0, 'max' => 5],
            'cc_thickness' => ['min' => 0, 'max' => 1],
            'reinforcement' => ['min' => 0, 'max' => 1000],
            'wall_tile_height' => ['min' => 0, 'max' => 5],
            'wall_height' => ['min' => 0, 'max' => 10],
            'basement_width' => ['min' => 0, 'max' => 5],
            'basement_height' => ['min' => 0, 'max' => 5],
            'window_kw3_height' => ['min' => 0, 'max' => 5],
            'window_v1_height' => ['min' => 0, 'max' => 5],
            'window_w1_height' => ['min' => 0, 'max' => 5],
            'window_w2_height' => ['min' => 0, 'max' => 5],
            'window_w3_height' => ['min' => 0, 'max' => 5],
            'window_w4_height' => ['min' => 0, 'max' => 5]
        ];
        
        foreach ($numeric_fields as $field => $validation) {
            $value = trim($_POST[$field] ?? '');
            if (empty($value)) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " is required";
            } elseif (!is_numeric($value)) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " must be a valid number";
            } elseif ($value < $validation['min'] || $value > $validation['max']) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " must be between {$validation['min']} and {$validation['max']}";
            } else {
                $input_settings[$field] = (float)$value;
            }
        }
        
        // Currency validations
        $currency_fields = ['electrical_works', 'plumbing_work', 'unforeseen_items'];
        
        foreach ($currency_fields as $field) {
            $value = trim($_POST[$field] ?? '');
            // Remove commas and currency symbols
            $value = preg_replace('/[,₹$]/', '', $value);
            
            if (empty($value)) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " is required";
            } elseif (!is_numeric($value)) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " must be a valid amount";
            } elseif ($value < 0 || $value > 10000000) {
                $errors[] = ucfirst(str_replace('_', ' ', $field)) . " must be between 0 and 10,000,000";
            } else {
                $input_settings[$field] = (float)$value;
            }
        }
        
        // If no errors, save to database
        if (empty($errors)) {
            try {
                // Check if settings already exist
                $check_stmt = $pdo->prepare("SELECT id FROM project_settings WHERE project_id = ?");
                $check_stmt->execute([$selected_project_id]);
                $existing = $check_stmt->fetch();
                
                if ($existing) {
                    // Update existing settings
                    $update_query = "
                        UPDATE project_settings SET
                        excavation_depth = ?, beam_depth = ?, slab_thickness = ?, door_height = ?,
                        cc_thickness = ?, reinforcement = ?, wall_tile_height = ?, electrical_works = ?,
                        plumbing_work = ?, unforeseen_items = ?, wall_height = ?, basement_width = ?,
                        basement_height = ?, window_kw3_height = ?, window_v1_height = ?, window_w1_height = ?,
                        window_w2_height = ?, window_w3_height = ?, window_w4_height = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE project_id = ?
                    ";
                    $update_stmt = $pdo->prepare($update_query);
                    $update_stmt->execute([
                        $input_settings['excavation_depth'], $input_settings['beam_depth'],
                        $input_settings['slab_thickness'], $input_settings['door_height'],
                        $input_settings['cc_thickness'], $input_settings['reinforcement'],
                        $input_settings['wall_tile_height'], $input_settings['electrical_works'],
                        $input_settings['plumbing_work'], $input_settings['unforeseen_items'],
                        $input_settings['wall_height'], $input_settings['basement_width'],
                        $input_settings['basement_height'], $input_settings['window_kw3_height'],
                        $input_settings['window_v1_height'], $input_settings['window_w1_height'],
                        $input_settings['window_w2_height'], $input_settings['window_w3_height'],
                        $input_settings['window_w4_height'], $selected_project_id
                    ]);
                    $success_message = "Project settings updated successfully!";
                } else {
                    // Insert new settings
                    $insert_query = "
                        INSERT INTO project_settings (
                            project_id, excavation_depth, beam_depth, slab_thickness, door_height,
                            cc_thickness, reinforcement, wall_tile_height, electrical_works,
                            plumbing_work, unforeseen_items, wall_height, basement_width,
                            basement_height, window_kw3_height, window_v1_height, window_w1_height,
                            window_w2_height, window_w3_height, window_w4_height
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ";
                    $insert_stmt = $pdo->prepare($insert_query);
                    $insert_stmt->execute([
                        $selected_project_id, $input_settings['excavation_depth'], $input_settings['beam_depth'],
                        $input_settings['slab_thickness'], $input_settings['door_height'],
                        $input_settings['cc_thickness'], $input_settings['reinforcement'],
                        $input_settings['wall_tile_height'], $input_settings['electrical_works'],
                        $input_settings['plumbing_work'], $input_settings['unforeseen_items'],
                        $input_settings['wall_height'], $input_settings['basement_width'],
                        $input_settings['basement_height'], $input_settings['window_kw3_height'],
                        $input_settings['window_v1_height'], $input_settings['window_w1_height'],
                        $input_settings['window_w2_height'], $input_settings['window_w3_height'],
                        $input_settings['window_w4_height']
                    ]);
                    $success_message = "Project settings saved successfully!";
                }
                
                // Update current settings with saved values
                $current_settings = $input_settings;
                
            } catch (Exception $e) {
                $errors[] = "Error saving settings: " . $e->getMessage();
            }
        }
    }
    
} catch (Exception $e) {
    $errors[] = "Database error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Settings - SmartEstimate 3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        .project-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .project-selector h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="number"],
        input[type="text"],
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="number"]:focus,
        input[type="text"]:focus,
        select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .currency-input {
            position: relative;
        }
        
        .currency-input::before {
            content: '₹';
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
            font-weight: bold;
        }
        
        .currency-input input {
            padding-left: 30px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .settings-form {
            display: grid;
            gap: 20px;
        }
        
        .section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        
        .section h3 {
            color: #007bff;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
        
        .field-description {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Project Settings</h1>

        <?php if (!empty($errors)): ?>
            <div class="error">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- Project Selector -->
        <div class="project-selector">
            <h3>Select Project</h3>
            <form method="GET">
                <div class="form-group">
                    <label for="project_id">Choose Project:</label>
                    <select id="project_id" name="project_id" onchange="this.form.submit()">
                        <option value="">-- Select a Project --</option>
                        <?php foreach ($projects as $proj): ?>
                            <option value="<?php echo $proj['id']; ?>"
                                    <?php echo ($selected_project_id == $proj['id']) ? 'selected' : ''; ?>>
                                #<?php echo $proj['id']; ?> - <?php echo htmlspecialchars($proj['rs_no']); ?>
                                (<?php echo htmlspecialchars($proj['client_name']); ?>)
                                - <?php echo ucfirst($proj['processing_status']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>

        <?php if ($selected_project_id && isset($project)): ?>
            <!-- Settings Form -->
            <form method="POST" class="settings-form">
                <input type="hidden" name="project_id" value="<?php echo $selected_project_id; ?>">

                <!-- Project Info -->
                <div class="section">
                    <h3>Project Information</h3>
                    <p><strong>Project ID:</strong> <?php echo $project['id']; ?></p>
                    <p><strong>R.S. No:</strong> <?php echo htmlspecialchars($project['rs_no']); ?></p>
                    <p><strong>Client:</strong> <?php echo htmlspecialchars($project['client_name']); ?></p>
                </div>

                <!-- Construction Dimensions -->
                <div class="section">
                    <h3>Construction Dimensions</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="excavation_depth">Excavation Depth (meters)</label>
                            <input type="number" id="excavation_depth" name="excavation_depth"
                                   value="<?php echo $current_settings['excavation_depth']; ?>"
                                   step="0.01" min="0" max="10" required>
                            <div class="field-description">Default: 1.00m</div>
                        </div>

                        <div class="form-group">
                            <label for="beam_depth">Beam Depth (meters)</label>
                            <input type="number" id="beam_depth" name="beam_depth"
                                   value="<?php echo $current_settings['beam_depth']; ?>"
                                   step="0.01" min="0" max="2" required>
                            <div class="field-description">Default: 0.20m</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="slab_thickness">Slab Thickness (meters)</label>
                            <input type="number" id="slab_thickness" name="slab_thickness"
                                   value="<?php echo $current_settings['slab_thickness']; ?>"
                                   step="0.01" min="0" max="1" required>
                            <div class="field-description">Default: 0.13m</div>
                        </div>

                        <div class="form-group">
                            <label for="door_height">Door Height (meters)</label>
                            <input type="number" id="door_height" name="door_height"
                                   value="<?php echo $current_settings['door_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 2.10m</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="cc_thickness">CC Thickness (meters)</label>
                            <input type="number" id="cc_thickness" name="cc_thickness"
                                   value="<?php echo $current_settings['cc_thickness']; ?>"
                                   step="0.01" min="0" max="1" required>
                            <div class="field-description">Default: 0.05m</div>
                        </div>

                        <div class="form-group">
                            <label for="reinforcement">Reinforcement (kg/m³)</label>
                            <input type="number" id="reinforcement" name="reinforcement"
                                   value="<?php echo $current_settings['reinforcement']; ?>"
                                   step="0.01" min="0" max="1000" required>
                            <div class="field-description">Default: 110 kg/m³</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="wall_tile_height">Wall Tile Height (meters)</label>
                            <input type="number" id="wall_tile_height" name="wall_tile_height"
                                   value="<?php echo $current_settings['wall_tile_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 2.10m</div>
                        </div>

                        <div class="form-group">
                            <label for="wall_height">Wall Height (meters)</label>
                            <input type="number" id="wall_height" name="wall_height"
                                   value="<?php echo $current_settings['wall_height']; ?>"
                                   step="0.01" min="0" max="10" required>
                            <div class="field-description">Default: 3.00m</div>
                        </div>
                    </div>
                </div>

                <!-- Basement Dimensions -->
                <div class="section">
                    <h3>Basement Dimensions</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="basement_width">Basement Width (meters)</label>
                            <input type="number" id="basement_width" name="basement_width"
                                   value="<?php echo $current_settings['basement_width']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 0.30m</div>
                        </div>

                        <div class="form-group">
                            <label for="basement_height">Basement Height (meters)</label>
                            <input type="number" id="basement_height" name="basement_height"
                                   value="<?php echo $current_settings['basement_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 0.60m</div>
                        </div>
                    </div>
                </div>

                <!-- Default Window Heights -->
                <div class="section">
                    <h3>Default Window Heights</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="window_kw3_height">Height of WINDOW_KW3 (meters)</label>
                            <input type="number" id="window_kw3_height" name="window_kw3_height"
                                   value="<?php echo $current_settings['window_kw3_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 1.10m</div>
                        </div>

                        <div class="form-group">
                            <label for="window_v1_height">Height of WINDOW_V1 (meters)</label>
                            <input type="number" id="window_v1_height" name="window_v1_height"
                                   value="<?php echo $current_settings['window_v1_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 0.60m</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="window_w1_height">Height of WINDOW_W1 (meters)</label>
                            <input type="number" id="window_w1_height" name="window_w1_height"
                                   value="<?php echo $current_settings['window_w1_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 1.50m</div>
                        </div>

                        <div class="form-group">
                            <label for="window_w2_height">Height of WINDOW_W2 (meters)</label>
                            <input type="number" id="window_w2_height" name="window_w2_height"
                                   value="<?php echo $current_settings['window_w2_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 1.50m</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="window_w3_height">Height of WINDOW_W3 (meters)</label>
                            <input type="number" id="window_w3_height" name="window_w3_height"
                                   value="<?php echo $current_settings['window_w3_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 1.50m</div>
                        </div>

                        <div class="form-group">
                            <label for="window_w4_height">Height of WINDOW_W4 (meters)</label>
                            <input type="number" id="window_w4_height" name="window_w4_height"
                                   value="<?php echo $current_settings['window_w4_height']; ?>"
                                   step="0.01" min="0" max="5" required>
                            <div class="field-description">Default: 1.50m</div>
                        </div>
                    </div>
                </div>

                <!-- Cost Estimates -->
                <div class="section">
                    <h3>Cost Estimates</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="electrical_works">Electrical Works (including materials & labour)</label>
                            <div class="currency-input">
                                <input type="text" id="electrical_works" name="electrical_works"
                                       value="<?php echo number_format($current_settings['electrical_works'], 2); ?>"
                                       required>
                            </div>
                            <div class="field-description">Default: ₹1,00,000.00</div>
                        </div>

                        <div class="form-group">
                            <label for="plumbing_work">Plumbing Work</label>
                            <div class="currency-input">
                                <input type="text" id="plumbing_work" name="plumbing_work"
                                       value="<?php echo number_format($current_settings['plumbing_work'], 2); ?>"
                                       required>
                            </div>
                            <div class="field-description">Default: ₹1,00,000.00</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="unforeseen_items">Unforeseen Items & Misc. Demolishing Works</label>
                            <div class="currency-input">
                                <input type="text" id="unforeseen_items" name="unforeseen_items"
                                       value="<?php echo number_format($current_settings['unforeseen_items'], 2); ?>"
                                       required>
                            </div>
                            <div class="field-description">Default: ₹10,000.00</div>
                        </div>
                        <div></div> <!-- Empty cell for grid alignment -->
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="form-group">
                    <button type="submit" class="btn">Save Settings</button>
                    <a href="view_projects.php" class="btn btn-secondary">Back to Projects</a>
                </div>
            </form>
        <?php elseif ($selected_project_id): ?>
            <div class="error">
                <p>Project not found. Please select a valid project from the dropdown above.</p>
            </div>
        <?php else: ?>
            <div style="text-align: center; padding: 40px; color: #666;">
                <p>Please select a project from the dropdown above to configure its settings.</p>
            </div>
        <?php endif; ?>

        <div class="nav-links">
            <a href="index.php">Dashboard</a>
            <a href="view_projects.php">View Projects</a>
            <a href="add_project.php">Add Project</a>
        </div>
    </div>

    <script>
        // Format currency inputs
        document.addEventListener('DOMContentLoaded', function() {
            const currencyInputs = document.querySelectorAll('.currency-input input');

            currencyInputs.forEach(input => {
                input.addEventListener('blur', function() {
                    let value = this.value.replace(/[,₹$]/g, '');
                    if (value && !isNaN(value)) {
                        this.value = parseFloat(value).toLocaleString('en-IN', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                    }
                });

                input.addEventListener('focus', function() {
                    this.value = this.value.replace(/[,₹$]/g, '');
                });
            });
        });
    </script>
</body>
</html>
