<?php
require_once 'config/database.php';

// Get project ID from URL parameter
$project_id = $_GET['project_id'] ?? null;

if (!$project_id) {
    die("Project ID is required. Please access this page from the project list.");
}

try {
    $pdo = getDatabaseConnection();
    
    // Get project details
    $project_stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ?");
    $project_stmt->execute([$project_id]);
    $project = $project_stmt->fetch();
    
    if (!$project) {
        die("Project not found.");
    }
    
    // Get project settings
    $settings_stmt = $pdo->prepare("SELECT * FROM project_settings WHERE project_id = ?");
    $settings_stmt->execute([$project_id]);
    $settings = $settings_stmt->fetch();
    
    // Use default values if no settings found
    $excavation_depth = $settings['excavation_depth'] ?? 1.00;
    $wall_height = $settings['wall_height'] ?? 3.00;
    $basement_width = $settings['basement_width'] ?? 0.30;
    $basement_height = $settings['basement_height'] ?? 0.60;
    $lintel_thickness = $settings['lintel_thickness'] ?? 0.23;
    $main_slab_thickness = $settings['main_slab_thickness'] ?? 0.125;
    $staircase_steps = $settings['staircase_steps'] ?? 20;
    $beam_depth = $settings['beam_depth'] ?? 0.20;
    $cc_thickness = $settings['cc_thickness'] ?? 0.05;
    $slab_thickness = $settings['slab_thickness'] ?? 0.13;
    $window_heights = [
        'WINDOW_KW3' => $settings['window_kw3_height'] ?? 1.10,
        'WINDOW_V1' => $settings['window_v1_height'] ?? 0.60,
        'WINDOW_W1' => $settings['window_w1_height'] ?? 1.50,
        'WINDOW_W2' => $settings['window_w2_height'] ?? 1.50,
        'WINDOW_W3' => $settings['window_w3_height'] ?? 1.50,
        'WINDOW_W4' => $settings['window_w4_height'] ?? 1.50,
    ];
    $door_height = $settings['door_height'] ?? 2.10;
    
    // Get all floors
    $floors_stmt = $pdo->prepare("SELECT * FROM floors WHERE project_id = ? ORDER BY id");
    $floors_stmt->execute([$project_id]);
    $floors = $floors_stmt->fetchAll();
    
    // Find ground floor (assuming first floor or floor with 'GROUND' in floor_name)
    $ground_floor = null;
    foreach ($floors as $floor) {
        if (stripos($floor['floor_name'], 'GROUND') !== false || $floor['id'] == $floors[0]['id']) {
            $ground_floor = $floor;
            break;
        }
    }
    
    if (!$ground_floor) {
        $ground_floor = $floors[0] ?? null;
    }
    
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Calculation Functions
function calculateExcavation($pdo, $project_id, $ground_floor_id, $excavation_depth) {
    $stmt = $pdo->prepare("
        SELECT w.*, f.floor_name
        FROM walls w
        LEFT JOIN floors f ON w.floor_id = f.id
        WHERE w.project_id = ? AND w.floor_id = ?
        ORDER BY w.layer_name, w.length
    ");
    $stmt->execute([$project_id, $ground_floor_id]);
    $walls = $stmt->fetchAll();
    
    $items = [];
    $total_qty = 0;
    $rate = 145.20;
    
    foreach ($walls as $index => $wall) {
        $no = 1; // Each wall counted separately
        $l = $wall['length'];
        $b = 0.7; // Fixed width
        $d = $excavation_depth;
        $qty = $no * $l * $b * $d;
        $total_qty += $qty;
        
        $items[] = [
            'sl_no' => $index + 1,
            'description' => "Wall {$wall['layer_name']} - Length {$l}m",
            'no' => $no,
            'l' => $l,
            'b' => $b,
            'd' => $d,
            'qty' => $qty
        ];
    }
    
    return [
        'items' => $items,
        'total_qty' => $total_qty,
        'rate' => $rate,
        'amount' => $total_qty * $rate,
        'description_summary' => "Total {$total_qty} M³ @ Rs.{$rate} per M³"
    ];
}

function calculateFoundationMasonry($pdo, $project_id, $ground_floor_id, $excavation_depth, $basement_width, $basement_height) {
    $stmt = $pdo->prepare("
        SELECT w.*, f.floor_name
        FROM walls w
        LEFT JOIN floors f ON w.floor_id = f.id
        WHERE w.project_id = ? AND w.floor_id = ?
        ORDER BY w.layer_name, w.length
    ");
    $stmt->execute([$project_id, $ground_floor_id]);
    $walls = $stmt->fetchAll();
    
    $below_gl_items = [];
    $basement_items = [];
    $below_gl_qty = 0;
    $basement_qty = 0;
    $rate = 4190.00;
    
    foreach ($walls as $index => $wall) {
        // Below Ground Level
        $no = 1;
        $l = $wall['length'];
        $b_below = 0.6;
        $d_below = $excavation_depth;
        $qty_below = $no * $l * $b_below * $d_below;
        $below_gl_qty += $qty_below;
        
        $below_gl_items[] = [
            'sl_no' => $index + 1,
            'description' => "Below GL - Wall {$wall['layer_name']} - Length {$l}m",
            'no' => $no,
            'l' => $l,
            'b' => $b_below,
            'd' => $d_below,
            'qty' => $qty_below
        ];
        
        // Basement
        $b_basement = $basement_width;
        $d_basement = $basement_height;
        $qty_basement = $no * $l * $b_basement * $d_basement;
        $basement_qty += $qty_basement;
        
        $basement_items[] = [
            'sl_no' => $index + 1,
            'description' => "Basement - Wall {$wall['layer_name']} - Length {$l}m",
            'no' => $no,
            'l' => $l,
            'b' => $b_basement,
            'd' => $d_basement,
            'qty' => $qty_basement
        ];
    }
    
    $total_qty = $below_gl_qty + $basement_qty;
    
    return [
        'below_gl_items' => $below_gl_items,
        'basement_items' => $basement_items,
        'below_gl_qty' => $below_gl_qty,
        'basement_qty' => $basement_qty,
        'total_qty' => $total_qty,
        'rate' => $rate,
        'amount' => $total_qty * $rate,
        'description_summary' => "Total {$total_qty} M³ @ Rs.{$rate} per M³"
    ];
}

function calculateSuperstructureMasonry($pdo, $project_id, $floors, $wall_height) {
    $items = [];
    $total_qty = 0;
    $rate = 4314.00;
    $item_counter = 1;
    
    foreach ($floors as $floor) {
        $stmt = $pdo->prepare("
            SELECT w.*, f.floor_name
            FROM walls w
            LEFT JOIN floors f ON w.floor_id = f.id
            WHERE w.project_id = ? AND w.floor_id = ?
            ORDER BY w.layer_name, w.length
        ");
        $stmt->execute([$project_id, $floor['id']]);
        $walls = $stmt->fetchAll();
        
        foreach ($walls as $wall) {
            $no = 1;
            $l = $wall['length'];
            $b = $wall['thickness']; // Wall thickness from database
            $d = $wall_height;
            $qty = $no * $l * $b * $d;
            $total_qty += $qty;
            
            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Floor: {$floor['floor_name']} - Wall {$wall['layer_name']} - Length {$l}m",
                'no' => $no,
                'l' => $l,
                'b' => $b,
                'd' => $d,
                'qty' => $qty
            ];
        }
    }
    
    return [
        'items' => $items,
        'total_qty' => $total_qty,
        'rate' => $rate,
        'amount' => $total_qty * $rate
    ];
}

function calculateOpeningDeductions($pdo, $project_id, $window_heights, $door_height) {
    $deduction_items = [];
    $total_deduction_qty = 0;
    $item_counter = 1;
    
    // Get doors
    $doors_stmt = $pdo->prepare("
        SELECT d.*, f.floor_name
        FROM doors d
        LEFT JOIN floors f ON d.floor_id = f.id
        WHERE d.project_id = ?
        ORDER BY d.layer_name
    ");
    $doors_stmt->execute([$project_id]);
    $doors = $doors_stmt->fetchAll();
    
    foreach ($doors as $door) {
        $no = $door['count'];
        $l = $door['length'];
        $b = $door['width'];
        $d = $door_height;
        $qty = $no * $l * $b * $d;
        $total_deduction_qty += $qty;
        
        $deduction_items[] = [
            'sl_no' => $item_counter++,
            'description' => "Door: {$door['layer_name']} - Floor: {$door['floor_name']}",
            'no' => $no,
            'l' => $l,
            'b' => $b,
            'd' => $d,
            'qty' => $qty
        ];
    }
    
    // Get windows
    $windows_stmt = $pdo->prepare("
        SELECT w.*, f.floor_name
        FROM windows w
        LEFT JOIN floors f ON w.floor_id = f.id
        WHERE w.project_id = ?
        ORDER BY w.layer_name
    ");
    $windows_stmt->execute([$project_id]);
    $windows = $windows_stmt->fetchAll();
    
    foreach ($windows as $window) {
        $window_height = $window_heights[$window['layer_name']] ?? 1.50;
        $no = $window['count'];
        $l = $window['length'];
        $b = $window['width'];
        $d = $window_height;
        $qty = $no * $l * $b * $d;
        $total_deduction_qty += $qty;
        
        $deduction_items[] = [
            'sl_no' => $item_counter++,
            'description' => "Window: {$window['layer_name']} - Floor: {$window['floor_name']}",
            'no' => $no,
            'l' => $l,
            'b' => $b,
            'd' => $d,
            'qty' => $qty
        ];
    }
    
    // Get openings
    $openings_stmt = $pdo->prepare("
        SELECT o.*, f.floor_name
        FROM openings o
        LEFT JOIN floors f ON o.floor_id = f.id
        WHERE o.project_id = ?
        ORDER BY o.layer_name
    ");
    $openings_stmt->execute([$project_id]);
    $openings = $openings_stmt->fetchAll();
    
    foreach ($openings as $opening) {
        $no = $opening['count'];
        $l = $opening['length'];
        $b = $opening['width'];
        $d = $door_height; // Using door height for openings
        $qty = $no * $l * $b * $d;
        $total_deduction_qty += $qty;
        
        $deduction_items[] = [
            'sl_no' => $item_counter++,
            'description' => "Opening: {$opening['layer_name']} - Floor: {$opening['floor_name']}",
            'no' => $no,
            'l' => $l,
            'b' => $b,
            'd' => $d,
            'qty' => $qty
        ];
    }
    
    return [
        'items' => $deduction_items,
        'total_qty' => $total_deduction_qty
    ];
}

function calculateAnjilywoodFramework($pdo, $project_id) {
    // Fixed dimensions table for each tag
    $tag_dimensions = [
        'MD' => [
            ['multiplier' => 2, 'l' => 0.25, 'b' => 0.080, 'd' => 1.35],
            ['multiplier' => 2, 'l' => 0.25, 'b' => 0.080, 'd' => 2.10]
        ],
        'D1' => [
            ['multiplier' => 1, 'l' => 0.13, 'b' => 0.080, 'd' => 1.20],
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 2.10]
        ],
        'D2' => [
            ['multiplier' => 1, 'l' => 0.13, 'b' => 0.080, 'd' => 1.05],
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 2.30]
        ],
        'W2' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 1.30],
            ['multiplier' => 3, 'l' => 0.13, 'b' => 0.080, 'd' => 1.50]
        ],
        'W4' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 2.30],
            ['multiplier' => 5, 'l' => 0.13, 'b' => 0.080, 'd' => 1.50]
        ],
        'W3' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 1.80],
            ['multiplier' => 4, 'l' => 0.13, 'b' => 0.080, 'd' => 1.50]
        ],
        'W1' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 1.80],
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 0.60]
        ],
        'KW3' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 1.80],
            ['multiplier' => 4, 'l' => 0.13, 'b' => 0.080, 'd' => 1.20]
        ],
        'V1' => [
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 0.90],
            ['multiplier' => 2, 'l' => 0.13, 'b' => 0.080, 'd' => 0.60]
        ]
    ];

    $items = [];
    $total_qty_m3 = 0;
    $item_counter = 1;
    $rate_per_10dm3 = 796;

    // Get all doors, windows, and openings grouped by layer name
    $stmt = $pdo->prepare("
        SELECT 'DOOR' as item_type, layer_name, SUM(count) as total_count, f.floor_name
        FROM doors d
        LEFT JOIN floors f ON d.floor_id = f.id
        WHERE d.project_id = ?
        GROUP BY layer_name, f.floor_name

        UNION ALL

        SELECT 'WINDOW' as item_type, layer_name, SUM(count) as total_count, f.floor_name
        FROM windows w
        LEFT JOIN floors f ON w.floor_id = f.id
        WHERE w.project_id = ?
        GROUP BY layer_name, f.floor_name

        UNION ALL

        SELECT 'OPENING' as item_type, layer_name, SUM(count) as total_count, f.floor_name
        FROM openings o
        LEFT JOIN floors f ON o.floor_id = f.id
        WHERE o.project_id = ? AND layer_name LIKE '%VENTILATOR%'
        GROUP BY layer_name, f.floor_name

        ORDER BY item_type, layer_name
    ");
    $stmt->execute([$project_id, $project_id, $project_id]);
    $grouped_items = $stmt->fetchAll();

    foreach ($grouped_items as $group) {
        // Extract tag from layer name (e.g., DOOR_MD -> MD, WINDOW_W2 -> W2)
        $layer_name = $group['layer_name'];
        $tag = '';

        if (strpos($layer_name, 'DOOR_') === 0) {
            $tag = substr($layer_name, 5); // Remove 'DOOR_'
        } elseif (strpos($layer_name, 'WINDOW_') === 0) {
            $tag = substr($layer_name, 7); // Remove 'WINDOW_'
        } elseif (strpos($layer_name, 'VENTILATOR') !== false) {
            $tag = 'V1'; // Default ventilator tag
        }

        // Check if we have dimensions for this tag
        if (!isset($tag_dimensions[$tag])) {
            continue; // Skip if no dimensions defined
        }

        $db_count = $group['total_count'];
        $dimensions = $tag_dimensions[$tag];

        // Process each line for this tag (usually 2 lines per tag)
        foreach ($dimensions as $line_index => $dim) {
            $no = $db_count * $dim['multiplier'];
            $l = $dim['l'];
            $b = $dim['b'];
            $d = $dim['d'];
            $qty_m3 = $no * $l * $b * $d;
            $total_qty_m3 += $qty_m3;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "{$group['item_type']} {$tag} Line " . ($line_index + 1) . " - Floor: {$group['floor_name']} (×{$dim['multiplier']})",
                'no' => $no,
                'l' => $l,
                'b' => $b,
                'd' => $d,
                'qty' => $qty_m3
            ];
        }
    }

    // Convert to dm³ and calculate amount
    $total_qty_dm3 = $total_qty_m3 * 1000;
    $amount = ($total_qty_dm3 / 10) * $rate_per_10dm3;

    return [
        'items' => $items,
        'total_qty_m3' => $total_qty_m3,
        'total_qty_dm3' => $total_qty_dm3,
        'rate_per_10dm3' => $rate_per_10dm3,
        'amount' => $amount,
        'description_summary' => "Total {$total_qty_dm3} dm³ @ Rs.{$rate_per_10dm3} per 10dm³"
    ];
}

function calculateAnjilywoodDoorShutters($pdo, $project_id) {
    // Fixed dimensions for each door type
    $door_dimensions = [
        'MD' => ['l' => 1.06, 'b' => 1.96],
        'D1' => ['l' => 0.86, 'b' => 1.96],
        'D2' => ['l' => 0.75, 'b' => 1.96]
    ];

    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_10dm2 = 424;

    // Get all doors grouped by layer name
    $stmt = $pdo->prepare("
        SELECT layer_name, SUM(count) as total_count, f.floor_name
        FROM doors d
        LEFT JOIN floors f ON d.floor_id = f.id
        WHERE d.project_id = ?
        GROUP BY layer_name, f.floor_name
        ORDER BY layer_name
    ");
    $stmt->execute([$project_id]);
    $grouped_doors = $stmt->fetchAll();

    foreach ($grouped_doors as $group) {
        // Extract tag from layer name (e.g., DOOR_MD -> MD, DOOR_D1 -> D1)
        $layer_name = $group['layer_name'];
        $tag = '';

        if (strpos($layer_name, 'DOOR_') === 0) {
            $tag = substr($layer_name, 5); // Remove 'DOOR_'
        }

        // Check if we have dimensions for this door type
        if (!isset($door_dimensions[$tag])) {
            continue; // Skip if no dimensions defined
        }

        $db_count = $group['total_count'];
        $dimensions = $door_dimensions[$tag];

        $no = $db_count;
        $l = $dimensions['l'];
        $b = $dimensions['b'];
        $d = null; // Not needed for area calculation
        $qty_m2 = $no * $l * $b;
        $total_qty_m2 += $qty_m2;

        $items[] = [
            'sl_no' => $item_counter++,
            'description' => "Door {$tag} Shutters - Floor: {$group['floor_name']} ({$l}m × {$b}m)",
            'no' => $no,
            'l' => $l,
            'b' => $b,
            'd' => null,
            'qty' => $qty_m2
        ];
    }

    // Convert to dm² and calculate amount
    $total_qty_dm2 = $total_qty_m2 * 100;
    $amount = ($total_qty_dm2 / 10) * $rate_per_10dm2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'total_qty_dm2' => $total_qty_dm2,
        'rate_per_10dm2' => $rate_per_10dm2,
        'amount' => $amount,
        'description_summary' => "Total {$total_qty_dm2} dm² @ Rs.{$rate_per_10dm2} per 10dm²"
    ];
}

function calculateMSGrills($pdo, $project_id) {
    // Specifications for each type
    $grill_specs = [
        'W3' => ['multiplier' => 3, 'weight_per_unit' => 15],
        'W4' => ['multiplier' => 4, 'weight_per_unit' => 15],
        'W2' => ['multiplier' => 2, 'weight_per_unit' => 15],
        'KW3' => ['multiplier' => 3, 'weight_per_unit' => 15],
        'W1' => ['multiplier' => 1, 'weight_per_unit' => 15],
        'V1' => ['multiplier' => 1, 'weight_per_unit' => 6]
    ];

    $items = [];
    $total_qty_kg = 0;
    $item_counter = 1;
    $rate_per_qtl = 10415;

    // Get all windows grouped by layer name
    $stmt = $pdo->prepare("
        SELECT 'WINDOW' as item_type, layer_name, SUM(count) as total_count, f.floor_name
        FROM windows w
        LEFT JOIN floors f ON w.floor_id = f.id
        WHERE w.project_id = ?
        GROUP BY layer_name, f.floor_name

        UNION ALL

        SELECT 'OPENING' as item_type, layer_name, SUM(count) as total_count, f.floor_name
        FROM openings o
        LEFT JOIN floors f ON o.floor_id = f.id
        WHERE o.project_id = ? AND layer_name LIKE '%VENTILATOR%'
        GROUP BY layer_name, f.floor_name

        ORDER BY item_type, layer_name
    ");
    $stmt->execute([$project_id, $project_id]);
    $grouped_items = $stmt->fetchAll();

    foreach ($grouped_items as $group) {
        // Extract tag from layer name (e.g., WINDOW_W3 -> W3, VENTILATOR -> V1)
        $layer_name = $group['layer_name'];
        $tag = '';

        if (strpos($layer_name, 'WINDOW_') === 0) {
            $tag = substr($layer_name, 7); // Remove 'WINDOW_'
        } elseif (strpos($layer_name, 'VENTILATOR') !== false) {
            $tag = 'V1'; // Default ventilator tag
        }

        // Check if we have specifications for this tag
        if (!isset($grill_specs[$tag])) {
            continue; // Skip if no specifications defined
        }

        $db_count = $group['total_count'];
        $specs = $grill_specs[$tag];

        $multiplier = $specs['multiplier'];
        $weight_per_unit = $specs['weight_per_unit'];
        $qty_kg = $db_count * $multiplier * $weight_per_unit;
        $total_qty_kg += $qty_kg;

        $items[] = [
            'sl_no' => $item_counter++,
            'description' => "{$group['item_type']} {$tag} Grills - Floor: {$group['floor_name']} (×{$multiplier} × {$weight_per_unit}kg)",
            'no' => $db_count,
            'l' => $multiplier,
            'b' => $weight_per_unit,
            'd' => null,
            'qty' => $qty_kg
        ];
    }

    // Convert to quintals and calculate amount
    $total_qty_qtl = $total_qty_kg / 100;
    $amount = $total_qty_qtl * $rate_per_qtl;

    return [
        'items' => $items,
        'total_qty_kg' => $total_qty_kg,
        'total_qty_qtl' => $total_qty_qtl,
        'rate_per_qtl' => $rate_per_qtl,
        'amount' => $amount,
        'description_summary' => "Total {$total_qty_qtl} Qtl @ Rs.{$rate_per_qtl} per Qtl"
    ];
}

function calculateRCCWork($pdo, $project_id, $floors, $lintel_thickness, $main_slab_thickness, $wall_height, $staircase_steps, $beam_depth) {
    $items = [];
    $total_qty_m3 = 0;
    $item_counter = 1;
    $rate_per_10dm3 = 85.69;

    // Process each floor separately
    foreach ($floors as $floor) {
        $floor_name = $floor['floor_name'];
        $floor_id = $floor['id'];

        // 1. LINTEL - Get walls for this floor
        $walls_stmt = $pdo->prepare("
            SELECT length, width FROM walls
            WHERE project_id = ? AND floor_id = ?
            ORDER BY layer_name, length
        ");
        $walls_stmt->execute([$project_id, $floor_id]);
        $walls = $walls_stmt->fetchAll();

        foreach ($walls as $wall) {
            $no = 1;
            $l = $wall['length'];
            $b = $wall['width']; // From database
            $d = $lintel_thickness;
            $qty = $no * $l * $b * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Lintel - Length {$l}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'lintel'
            ];
        }

        // 2. SUNSHADE - Get outside openings for this floor (outer_side = 1)
        $sunshade_stmt = $pdo->prepare("
            SELECT 'DOOR' as type, layer_name, length, count FROM doors
            WHERE project_id = ? AND floor_id = ? AND outer_side = 1

            UNION ALL

            SELECT 'WINDOW' as type, layer_name, length, count FROM windows
            WHERE project_id = ? AND floor_id = ? AND outer_side = 1

            UNION ALL

            SELECT 'OPENING' as type, layer_name, length, count FROM openings
            WHERE project_id = ? AND floor_id = ? AND outer_side = 1

            ORDER BY type, layer_name
        ");
        $sunshade_stmt->execute([$project_id, $floor_id, $project_id, $floor_id, $project_id, $floor_id]);
        $sunshades = $sunshade_stmt->fetchAll();

        foreach ($sunshades as $sunshade) {
            for ($i = 0; $i < $sunshade['count']; $i++) {
                $no = 1;
                $l = 0.6 + $sunshade['length']; // 0.6 + opening length
                $b = 0.6; // Fixed
                $d = 0.1; // Fixed
                $qty = $no * $l * $b * $d;
                $total_qty_m3 += $qty;

                $items[] = [
                    'sl_no' => $item_counter++,
                    'description' => "Sunshade - {$sunshade['type']} {$sunshade['layer_name']} - Length {$sunshade['length']}m",
                    'no' => $no,
                    'l' => round($l, 3),
                    'b' => round($b, 3),
                    'd' => round($d, 3),
                    'qty' => round($qty, 3),
                    'floor' => $floor_name,
                    'type' => 'sunshade'
                ];
            }
        }

        // 3. MAIN SLAB - Get slab data for this floor
        $slab_stmt = $pdo->prepare("
            SELECT layer_name, area FROM slabs
            WHERE project_id = ? AND floor_id = ?
            ORDER BY layer_name
        ");
        $slab_stmt->execute([$project_id, $floor_id]);
        $slabs = $slab_stmt->fetchAll();

        foreach ($slabs as $slab) {
            if (strpos($slab['layer_name'], 'TOTAL') !== false) {
                $no = 1;
            } elseif (strpos($slab['layer_name'], 'DEDUCTION') !== false) {
                $no = -1;
            } else {
                $no = 1;
            }

            $merged_lb = $slab['area']; // Area represents L*B
            $d = $main_slab_thickness;
            $qty = $no * $merged_lb * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Main Slab - {$slab['layer_name']} - Area {$slab['area']}m²",
                'no' => $no,
                'l' => round($merged_lb, 3),
                'b' => '-',
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'slab'
            ];
        }

        // 4. STAIRCASE for this floor (if any)
        $staircase_stmt = $pdo->prepare("
            SELECT COUNT(*) as staircase_count FROM staircases WHERE project_id = ? AND floor_id = ?
        ");
        $staircase_stmt->execute([$project_id, $floor_id]);
        $staircase_data = $staircase_stmt->fetch();
        $staircase_count = $staircase_data['staircase_count'] ?? 0;

        if ($staircase_count > 0) {
            // Steps
            $no = $staircase_steps;
            $l = 1.00;
            $b = 0.254;
            $d = 0.16;
            $qty = $no * $l * $b * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Staircase - Steps ({$staircase_steps} steps)",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'staircase'
            ];

            // Landing
            $no = 2;
            $l = 1.00;
            $b = 1.00;
            $d = 0.10;
            $qty = $no * $l * $b * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Staircase - Landing (2 nos)",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'staircase'
            ];
        }

        // 5. COLUMNS for this floor
        $columns_stmt = $pdo->prepare("
            SELECT length, width, COUNT(*) as count FROM columns
            WHERE project_id = ? AND floor_id = ?
            GROUP BY length, width
            ORDER BY length, width
        ");
        $columns_stmt->execute([$project_id, $floor_id]);
        $columns = $columns_stmt->fetchAll();

        foreach ($columns as $column) {
            $no = $column['count'];
            $l = $column['length'];
            $b = $column['width'];
            $d = $wall_height;
            $qty = $no * $l * $b * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Columns - {$l}m × {$b}m ({$no} nos)",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'column'
            ];

            // Column Footings
            $l_footing = $l + 1.2;
            $b_footing = $b + 1.2;
            $d_footing = 0.4;
            $qty_footing = $no * $l_footing * $b_footing * $d_footing;
            $total_qty_m3 += $qty_footing;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Column Footings - " . round($l_footing, 3) . "m × " . round($b_footing, 3) . "m ({$no} nos)",
                'no' => $no,
                'l' => round($l_footing, 3),
                'b' => round($b_footing, 3),
                'd' => round($d_footing, 3),
                'qty' => round($qty_footing, 3),
                'floor' => $floor_name,
                'type' => 'footing'
            ];
        }

        // 6. BEAMS for this floor
        $beams_stmt = $pdo->prepare("
            SELECT length, width FROM beams
            WHERE project_id = ? AND floor_id = ?
            ORDER BY length, width
        ");
        $beams_stmt->execute([$project_id, $floor_id]);
        $beams = $beams_stmt->fetchAll();

        foreach ($beams as $beam) {
            $no = 1;
            $l = $beam['length'];
            $b = $beam['width'];
            $d = $beam_depth; // From project settings
            $qty = $no * $l * $b * $d;
            $total_qty_m3 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Beam - Length {$l}m × Width {$b}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'beam'
            ];
        }
    }


    // Convert to dm³ and calculate amount
    $total_qty_dm3 = $total_qty_m3 * 1000;
    $amount = ($total_qty_dm3 / 10) * $rate_per_10dm3;

    return [
        'items' => $items,
        'total_qty_m3' => $total_qty_m3,
        'total_qty_dm3' => $total_qty_dm3,
        'rate_per_10dm3' => $rate_per_10dm3,
        'amount' => $amount,
        'description_summary' => "Total {$total_qty_dm3} dm³ @ Rs.{$rate_per_10dm3} per 10dm³"
    ];
}

function calculateCCWork($pdo, $project_id, $ground_floor_id, $cc_thickness) {
    $items = [];
    $total_qty_m3 = 0;
    $item_counter = 1;
    $rate_per_m3 = 5622;

    // Get flooring data for ground floor only
    $flooring_stmt = $pdo->prepare("
        SELECT layer_name, length, width FROM flooring
        WHERE project_id = ? AND floor_id = ?
        ORDER BY layer_name
    ");
    $flooring_stmt->execute([$project_id, $ground_floor_id]);
    $floorings = $flooring_stmt->fetchAll();

    foreach ($floorings as $flooring) {
        $no = 1;
        $l = $flooring['length'];
        $b = $flooring['width'];
        $d = $cc_thickness;
        $qty = $no * $l * $b * $d;
        $total_qty_m3 += $qty;

        $items[] = [
            'sl_no' => $item_counter++,
            'description' => "CC Work - {$l}m × {$b}m",
            'no' => $no,
            'l' => round($l, 3),
            'b' => round($b, 3),
            'd' => round($d, 3),
            'qty' => round($qty, 3),
            'floor' => 'Ground Floor',
            'type' => 'cc'
        ];
    }

    // Calculate amount
    $amount = $total_qty_m3 * $rate_per_m3;

    return [
        'items' => $items,
        'total_qty_m3' => $total_qty_m3,
        'rate_per_m3' => $rate_per_m3,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m3, 3) . " m³ @ Rs.{$rate_per_m3} per m³"
    ];
}

function calculateReinforcementWork($pdo, $project_id, $rcc_total_qty_m3, $reinforcement_kg_per_m3) {
    $items = [];
    $item_counter = 1;
    $rate_per_qtl = 7290;

    // Calculate total reinforcement in kg
    $total_qty_kg = $rcc_total_qty_m3 * $reinforcement_kg_per_m3;

    // Convert to quintals (1 Qtl = 100 kg)
    $total_qty_qtl = $total_qty_kg / 100;

    // Calculate amount
    $amount = $total_qty_qtl * $rate_per_qtl;

    $items[] = [
        'sl_no' => $item_counter++,
        'description' => "Reinforcement for R.C.C work bent, tied and placed in position - {$reinforcement_kg_per_m3} kg/m³ × " . round($rcc_total_qty_m3, 3) . " m³",
        'no' => 1,
        'l' => round($rcc_total_qty_m3, 3),
        'b' => $reinforcement_kg_per_m3,
        'd' => '-',
        'qty' => round($total_qty_kg, 2),
        'floor' => 'General',
        'type' => 'reinforcement'
    ];

    return [
        'items' => $items,
        'total_qty_kg' => $total_qty_kg,
        'total_qty_qtl' => $total_qty_qtl,
        'rate_per_qtl' => $rate_per_qtl,
        'amount' => $amount,
        'description_summary' => "(" . $reinforcement_kg_per_m3 . " kg/m³ × " . round($rcc_total_qty_m3, 3) . " m³) / 100 = " . round($total_qty_qtl, 4) . " Qtl @ Rs.{$rate_per_qtl} per Qtl"
    ];
}

function calculatePlasteringWork($pdo, $project_id, $floors, $rcc_items) {
    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_10m2 = 1834;

    // Process each floor separately
    foreach ($floors as $floor) {
        $floor_name = $floor['floor_name'];
        $floor_id = $floor['id'];

        // 1. ROOM CEILINGS - Get flooring data for this floor
        $flooring_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ?
            ORDER BY layer_name
        ");
        $flooring_stmt->execute([$project_id, $floor_id]);
        $floorings = $flooring_stmt->fetchAll();

        foreach ($floorings as $flooring) {
            $no = 1;
            $l = $flooring['length'];
            $b = $flooring['width'];
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Room Ceiling - {$l}m × {$b}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'room_ceiling'
            ];
        }

        // 2. SUNSHADE CEILINGS - Get sunshade data from RCC items for this floor
        foreach ($rcc_items as $rcc_item) {
            if ($rcc_item['floor'] == $floor_name && $rcc_item['type'] == 'sunshade') {
                $no = 1;
                $l = $rcc_item['l']; // Use the same length as in item 7
                $b = 0.6; // Fixed width for sunshade
                $qty = $no * $l * $b;
                $total_qty_m2 += $qty;

                $items[] = [
                    'sl_no' => $item_counter++,
                    'description' => "Sunshade Ceiling - " . str_replace('Sunshade - ', '', $rcc_item['description']),
                    'no' => $no,
                    'l' => round($l, 3),
                    'b' => round($b, 3),
                    'd' => '-',
                    'qty' => round($qty, 3),
                    'floor' => $floor_name,
                    'type' => 'sunshade_ceiling'
                ];
            }
        }
    }

    // 3. MAIN SLAB PROJECTION - Get slab projection data (MAIN_SLAB_TOTAL - FOOTPRINT)
    $projection_stmt = $pdo->prepare("
        SELECT
            s.floor_id,
            f.floor_name,
            s.area as slab_area,
            fp.area as footprint_area,
            (s.area - fp.area) as projection_area
        FROM slabs s
        JOIN floors f ON s.floor_id = f.id
        LEFT JOIN footprint fp ON s.project_id = fp.project_id AND s.floor_id = fp.floor_id
        WHERE s.project_id = ?
        AND s.layer_name LIKE '%MAIN_SLAB_TOTAL%'
        AND (s.area - COALESCE(fp.area, 0)) > 0
        ORDER BY f.floor_name
    ");
    $projection_stmt->execute([$project_id]);
    $projections = $projection_stmt->fetchAll();

    foreach ($projections as $projection) {
        $no = 1;
        $qty = $projection['projection_area'];
        $total_qty_m2 += $qty;

        $items[] = [
            'sl_no' => $item_counter++,
            'description' => "Main Slab Projection - Area " . round($qty, 3) . "m²",
            'no' => $no,
            'l' => round($qty, 3),
            'b' => '-',
            'd' => '-',
            'qty' => round($qty, 3),
            'floor' => $projection['floor_name'], // Use respective floor
            'type' => 'slab_projection'
        ];
    }

    // Calculate amount
    $amount = ($total_qty_m2 / 10) * $rate_per_10m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_10m2' => $rate_per_10m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_10m2} per 10m²"
    ];
}

function calculateWallPlasteringWork($pdo, $project_id, $floors, $wall_height, $basement_height, $slab_thickness, $window_heights, $door_height) {
    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_10m2 = 2127;

    // Process each floor separately
    foreach ($floors as $floor) {
        $floor_name = $floor['floor_name'];
        $floor_id = $floor['id'];

        // INSIDE WALLS - Get flooring data for this floor to calculate room perimeters
        $flooring_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ?
            ORDER BY layer_name
        ");
        $flooring_stmt->execute([$project_id, $floor_id]);
        $floorings = $flooring_stmt->fetchAll();

        foreach ($floorings as $flooring) {
            // Length walls (2 walls)
            $no = 2;
            $l = $flooring['length'];
            $d = $wall_height;
            $qty = $no * $l * $d;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Inside Wall - Length {$l}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => '-',
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'inside_wall'
            ];

            // Width walls (2 walls)
            $no = 2;
            $l = $flooring['width'];
            $d = $wall_height;
            $qty = $no * $l * $d;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Inside Wall - Width {$l}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => '-',
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'inside_wall'
            ];
        }

        // OUTSIDE WALLS - Get footprint perimeter for this floor
        $footprint_stmt = $pdo->prepare("
            SELECT perimeter FROM footprint
            WHERE project_id = ? AND floor_id = ?
        ");
        $footprint_stmt->execute([$project_id, $floor_id]);
        $footprint = $footprint_stmt->fetch();

        if ($footprint) {
            $no = 1;
            $l = $footprint['perimeter'];
            $d = $wall_height + $basement_height + $slab_thickness;
            $qty = $no * $l * $d;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Outside Wall - Perimeter {$l}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => '-',
                'd' => round($d, 3),
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'outside_wall'
            ];
        }

        // DEDUCTIONS - Get all doors, windows, openings for this floor
        $deductions_stmt = $pdo->prepare("
            SELECT 'DOOR' as type, layer_name, length, width, count FROM doors
            WHERE project_id = ? AND floor_id = ?

            UNION ALL

            SELECT 'WINDOW' as type, layer_name, length, width, count FROM windows
            WHERE project_id = ? AND floor_id = ?

            UNION ALL

            SELECT 'OPENING' as type, layer_name, length, width, count FROM openings
            WHERE project_id = ? AND floor_id = ?

            ORDER BY type, layer_name
        ");
        $deductions_stmt->execute([$project_id, $floor_id, $project_id, $floor_id, $project_id, $floor_id]);
        $deductions = $deductions_stmt->fetchAll();

        foreach ($deductions as $deduction) {
            for ($i = 0; $i < $deduction['count']; $i++) {
                $no = -1; // Negative for deduction
                $l = $deduction['length'];

                // Get height from project settings based on type
                if ($deduction['type'] == 'DOOR') {
                    $b = $door_height;
                } elseif ($deduction['type'] == 'WINDOW') {
                    // Get window height based on layer name
                    $layer_name = $deduction['layer_name'];
                    $b = $window_heights[$layer_name] ?? $wall_height; // Default to wall height if not found
                } else {
                    // For openings, use wall height as default
                    $b = $wall_height;
                }

                $qty = $no * $l * $b;
                $total_qty_m2 += $qty;

                $items[] = [
                    'sl_no' => $item_counter++,
                    'description' => "Deduction - {$deduction['type']} - {$l}m × {$b}m",
                    'no' => $no,
                    'l' => round($l, 3),
                    'b' => round($b, 3),
                    'd' => '-',
                    'qty' => round($qty, 3),
                    'floor' => $floor_name,
                    'type' => 'deduction'
                ];
            }
        }
    }

    // Calculate amount
    $amount = ($total_qty_m2 / 10) * $rate_per_10m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_10m2' => $rate_per_10m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_10m2} per 10m²"
    ];
}

function calculateElectricalWork($pdo, $project_id, $electrical_works_amount) {
    $items = [];
    $item_counter = 1;

    $items[] = [
        'sl_no' => $item_counter++,
        'description' => "Electrical works including cost of all materials and labour charges. Etc.complete.",
        'no' => 1,
        'l' => '-',
        'b' => '-',
        'd' => '-',
        'qty' => '-',
        'floor' => 'General',
        'type' => 'electrical'
    ];

    return [
        'items' => $items,
        'amount' => $electrical_works_amount,
        'description_summary' => "Electrical Works (including materials & labour) - Rs." . number_format($electrical_works_amount, 2)
    ];
}

function calculateGraniteFlooringWork($pdo, $project_id) {
    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_m2 = 3271.00;

    // Fetch kitchen slab data from rectangles table
    $kitchen_slab_stmt = $pdo->prepare("
        SELECT r.*, f.floor_name
        FROM rectangles r
        LEFT JOIN floors f ON r.floor_id = f.id
        WHERE r.project_id = ? AND r.layer_name = 'KITCHEN_SLAB'
        ORDER BY f.floor_name, r.length, r.width
    ");
    $kitchen_slab_stmt->execute([$project_id]);
    $kitchen_slabs = $kitchen_slab_stmt->fetchAll();

    foreach ($kitchen_slabs as $slab) {
        $no = 1; // Nos from database (assuming 1 for each slab)
        $l = $slab['length']; // Length from database
        $b = $slab['width'] + 0.15; // Width + 0.15m
        $qty = $no * $l * $b;
        $total_qty_m2 += $qty;

        $items[] = [
            'sl_no' => $item_counter++,
            'description' => "Granite Flooring - {$l}m × " . round($b, 3) . "m",
            'no' => $no,
            'l' => round($l, 3),
            'b' => round($b, 3),
            'd' => '-',
            'qty' => round($qty, 3),
            'floor' => $slab['floor_name'],
            'type' => 'granite_flooring'
        ];
    }

    // Calculate amount
    $amount = $total_qty_m2 * $rate_per_m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_m2' => $rate_per_m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_m2} per m²"
    ];
}

function calculateMainFlooringWork($pdo, $project_id, $floors) {
    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_m2 = 3271.00;

    // Process each floor separately
    foreach ($floors as $floor) {
        $floor_name = $floor['floor_name'];
        $floor_id = $floor['id'];

        // Get FLOORING_MAIN data for this floor
        $main_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ? AND layer_name = 'FLOORING_MAIN'
            ORDER BY layer_name
        ");
        $main_stmt->execute([$project_id, $floor_id]);
        $main_floorings = $main_stmt->fetchAll();

        foreach ($main_floorings as $flooring) {
            // Floor area
            $no = 1;
            $l = $flooring['length'];
            $b = $flooring['width'];
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Floor - {$l}m × {$b}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'main_floor'
            ];

            // Skirting - Length sides (2 sides)
            $no = 2;
            $l = $flooring['length'];
            $b = 0.1; // 0.1m height
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Skirting Length - {$l}m × 0.1m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'main_skirting'
            ];

            // Skirting - Width sides (2 sides)
            $no = 2;
            $l = $flooring['width'];
            $b = 0.1; // 0.1m height
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Skirting Width - {$l}m × 0.1m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'main_skirting'
            ];
        }

        // Get FLOORING_TOILET data for this floor
        $toilet_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ? AND layer_name = 'FLOORING_TOILET'
            ORDER BY layer_name
        ");
        $toilet_stmt->execute([$project_id, $floor_id]);
        $toilet_floorings = $toilet_stmt->fetchAll();

        foreach ($toilet_floorings as $flooring) {
            // Floor area only (no skirting for toilet)
            $no = 1;
            $l = $flooring['length'];
            $b = $flooring['width'];
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Toilet Floor - {$l}m × {$b}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'toilet_floor'
            ];
        }

        // Get FLOORING_KITCHEN data for this floor
        $kitchen_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ? AND layer_name = 'FLOORING_KITCHEN'
            ORDER BY layer_name
        ");
        $kitchen_stmt->execute([$project_id, $floor_id]);
        $kitchen_floorings = $kitchen_stmt->fetchAll();

        foreach ($kitchen_floorings as $flooring) {
            // Floor area only (no skirting for kitchen)
            $no = 1;
            $l = $flooring['length'];
            $b = $flooring['width'];
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Kitchen Floor - {$l}m × {$b}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'kitchen_floor'
            ];
        }
    }

    // Calculate amount
    $amount = $total_qty_m2 * $rate_per_m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_m2' => $rate_per_m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_m2} per m²"
    ];
}

function calculateWallDadoWork($pdo, $project_id, $floors, $door_height) {
    $items = [];
    $total_qty_m2 = 0;
    $item_counter = 1;
    $rate_per_m2 = 841.00;
    $dado_height = 2.1; // 2.1m height for dado

    // Process each floor separately
    foreach ($floors as $floor) {
        $floor_name = $floor['floor_name'];
        $floor_id = $floor['id'];

        // Get FLOORING_TOILET data for this floor
        $toilet_stmt = $pdo->prepare("
            SELECT layer_name, length, width FROM flooring
            WHERE project_id = ? AND floor_id = ? AND layer_name = 'FLOORING_TOILET'
            ORDER BY layer_name
        ");
        $toilet_stmt->execute([$project_id, $floor_id]);
        $toilet_floorings = $toilet_stmt->fetchAll();

        foreach ($toilet_floorings as $toilet) {
            // Length walls (2 walls)
            $no = 2;
            $l = $toilet['length'];
            $b = $dado_height;
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Toilet Dado Length - {$l}m × {$dado_height}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'toilet_dado'
            ];

            // Width walls (2 walls)
            $no = 2;
            $l = $toilet['width'];
            $b = $dado_height;
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Toilet Dado Width - {$l}m × {$dado_height}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'toilet_dado'
            ];

            // Door deduction for this toilet (one door per toilet - default 0.75m)
            $no = -1; // Negative for deduction
            $l = 0.75; // Default door width
            $b = $dado_height;
            $qty = $no * $l * $b;
            $total_qty_m2 += $qty;

            $items[] = [
                'sl_no' => $item_counter++,
                'description' => "Deduction Door - {$l}m × {$dado_height}m",
                'no' => $no,
                'l' => round($l, 3),
                'b' => round($b, 3),
                'd' => '-',
                'qty' => round($qty, 3),
                'floor' => $floor_name,
                'type' => 'toilet_deduction'
            ];
        }

    }

    // Calculate amount
    $amount = $total_qty_m2 * $rate_per_m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_m2' => $rate_per_m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_m2} per m²"
    ];
}

function calculatePaintingWork($pdo, $project_id, $ceiling_qty, $wall_qty) {
    $items = [];
    $item_counter = 1;
    $total_qty_m2 = $ceiling_qty + $wall_qty;
    $rate_per_10m2 = 728.00;

    $items[] = [
        'sl_no' => $item_counter++,
        'description' => "Painting - Sum of Item 10 & 11 quantities",
        'no' => 1,
        'l' => '-',
        'b' => '-',
        'd' => '-',
        'qty' => round($total_qty_m2, 3),
        'floor' => 'General',
        'type' => 'painting'
    ];

    // Calculate amount
    $amount = ($total_qty_m2 / 10) * $rate_per_10m2;

    return [
        'items' => $items,
        'total_qty_m2' => $total_qty_m2,
        'rate_per_10m2' => $rate_per_10m2,
        'amount' => $amount,
        'description_summary' => "Total " . round($total_qty_m2, 3) . " m² @ Rs.{$rate_per_10m2} per 10m²"
    ];
}

function calculatePlumbingWork($pdo, $project_id, $plumbing_amount) {
    $items = [];
    $item_counter = 1;

    $items[] = [
        'sl_no' => $item_counter++,
        'description' => "Plumbing work",
        'no' => 1,
        'l' => '-',
        'b' => '-',
        'd' => '-',
        'qty' => '-',
        'floor' => 'General',
        'type' => 'plumbing'
    ];

    return [
        'items' => $items,
        'amount' => $plumbing_amount,
        'description_summary' => "Plumbing Work - Rs." . number_format($plumbing_amount, 2)
    ];
}

function calculateUnforeseenWork($pdo, $project_id, $unforeseen_amount) {
    $items = [];
    $item_counter = 1;

    $items[] = [
        'sl_no' => $item_counter++,
        'description' => "Unforseen items & Misc. demolishing works",
        'no' => 1,
        'l' => '-',
        'b' => '-',
        'd' => '-',
        'qty' => '-',
        'floor' => 'General',
        'type' => 'unforeseen'
    ];

    return [
        'items' => $items,
        'amount' => $unforeseen_amount,
        'description_summary' => "Unforeseen Items & Misc. Demolishing Works - Rs." . number_format($unforeseen_amount, 2)
    ];
}

function numberToWords($number) {
    $ones = array(
        0 => '', 1 => 'One', 2 => 'Two', 3 => 'Three', 4 => 'Four', 5 => 'Five',
        6 => 'Six', 7 => 'Seven', 8 => 'Eight', 9 => 'Nine', 10 => 'Ten',
        11 => 'Eleven', 12 => 'Twelve', 13 => 'Thirteen', 14 => 'Fourteen', 15 => 'Fifteen',
        16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen', 19 => 'Nineteen'
    );

    $tens = array(
        0 => '', 2 => 'Twenty', 3 => 'Thirty', 4 => 'Forty', 5 => 'Fifty',
        6 => 'Sixty', 7 => 'Seventy', 8 => 'Eighty', 9 => 'Ninety'
    );

    if ($number == 0) return 'Zero';

    $words = '';

    // Handle crores
    if ($number >= 10000000) {
        $crores = intval($number / 10000000);
        $words .= convertHundreds($crores, $ones, $tens) . ' Crore ';
        $number %= 10000000;
    }

    // Handle lakhs
    if ($number >= 100000) {
        $lakhs = intval($number / 100000);
        $words .= convertHundreds($lakhs, $ones, $tens) . ' Lakh ';
        $number %= 100000;
    }

    // Handle thousands
    if ($number >= 1000) {
        $thousands = intval($number / 1000);
        $words .= convertHundreds($thousands, $ones, $tens) . ' Thousand ';
        $number %= 1000;
    }

    // Handle hundreds
    if ($number >= 100) {
        $hundreds = intval($number / 100);
        $words .= $ones[$hundreds] . ' Hundred ';
        $number %= 100;
    }

    // Handle remaining numbers
    if ($number > 0) {
        if ($number < 20) {
            $words .= $ones[$number];
        } else {
            $words .= $tens[intval($number / 10)];
            if ($number % 10 > 0) {
                $words .= ' ' . $ones[$number % 10];
            }
        }
    }

    return trim($words);
}

function convertHundreds($number, $ones, $tens) {
    $words = '';

    if ($number >= 100) {
        $words .= $ones[intval($number / 100)] . ' Hundred ';
        $number %= 100;
    }

    if ($number > 0) {
        if ($number < 20) {
            $words .= $ones[$number];
        } else {
            $words .= $tens[intval($number / 10)];
            if ($number % 10 > 0) {
                $words .= ' ' . $ones[$number % 10];
            }
        }
    }

    return trim($words);
}

// Perform calculations
$excavation = calculateExcavation($pdo, $project_id, $ground_floor['id'], $excavation_depth);
$foundation = calculateFoundationMasonry($pdo, $project_id, $ground_floor['id'], $excavation_depth, $basement_width, $basement_height);
$superstructure = calculateSuperstructureMasonry($pdo, $project_id, $floors, $wall_height);
$deductions = calculateOpeningDeductions($pdo, $project_id, $window_heights, $door_height);
$anjilywood = calculateAnjilywoodFramework($pdo, $project_id);
$door_shutters = calculateAnjilywoodDoorShutters($pdo, $project_id);
$ms_grills = calculateMSGrills($pdo, $project_id);
$rcc_work = calculateRCCWork($pdo, $project_id, $floors, $lintel_thickness, $main_slab_thickness, $wall_height, $staircase_steps, $beam_depth);
$cc_work = calculateCCWork($pdo, $project_id, $ground_floor['id'], $cc_thickness);
$reinforcement_work = calculateReinforcementWork($pdo, $project_id, $rcc_work['total_qty_m3'], $settings['reinforcement'] ?? 110.00);
$plastering_work = calculatePlasteringWork($pdo, $project_id, $floors, $rcc_work['items']);
$wall_plastering_work = calculateWallPlasteringWork($pdo, $project_id, $floors, $wall_height, $basement_height, $slab_thickness, $window_heights, $door_height);
$electrical_work = calculateElectricalWork($pdo, $project_id, $settings['electrical_works'] ?? 100000.00);
$granite_flooring_work = calculateGraniteFlooringWork($pdo, $project_id);
$main_flooring_work = calculateMainFlooringWork($pdo, $project_id, $floors);
$wall_dado_work = calculateWallDadoWork($pdo, $project_id, $floors, $door_height);
$painting_work = calculatePaintingWork($pdo, $project_id, $plastering_work['total_qty_m2'], $wall_plastering_work['total_qty_m2']);
$plumbing_work = calculatePlumbingWork($pdo, $project_id, $settings['plumbing_work'] ?? 75000.00);
$unforeseen_work = calculateUnforeseenWork($pdo, $project_id, $settings['unforeseen_items'] ?? 50000.00);

// Calculate final superstructure quantity after deductions
$final_superstructure_qty = $superstructure['total_qty'] - $deductions['total_qty'];
$final_superstructure_amount = $final_superstructure_qty * $superstructure['rate'];

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Construction Estimate - <?php echo htmlspecialchars($project['rs_no']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .header h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: normal;
        }
        
        .project-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .estimate-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            font-size: 14px;
        }
        
        .estimate-table th,
        .estimate-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        .estimate-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        
        .estimate-table .description {
            text-align: left;
            max-width: 300px;
        }
        
        .section-header {
            background-color: #28a745;
            color: white;
            font-weight: bold;
        }
        
        .subsection-header {
            background-color: #17a2b8;
            color: white;
            font-weight: bold;
        }
        
        .total-row {
            background-color: #ffc107;
            font-weight: bold;
        }
        
        .deduction-row {
            background-color: #dc3545;
            color: white;
        }
        
        .final-row {
            background-color: #28a745;
            color: white;
            font-weight: bold;
        }
        
        .amount {
            text-align: right;
            font-weight: bold;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
        
        .print-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        .print-btn:hover {
            background-color: #0056b3;
        }
        
        @media print {
            .nav-links, .print-btn, .project-info {
                display: none;
            }

            body {
                background: white;
                padding: 0;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                padding: 20px;
            }

            .header {
                border-bottom: 2px solid #000;
                border-top: 2px solid #000;
                padding: 15px 0;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 16px;
                margin-bottom: 8px;
            }

            .header h2 {
                font-size: 14px;
                margin-bottom: 8px;
            }

            .estimate-table {
                border: none;
                font-size: 11px;
            }

            .estimate-table th,
            .estimate-table td {
                border: none;
                background: white !important;
                color: black !important;
                padding: 4px;
            }

            .estimate-table th {
                border-top: 2px solid #000;
                border-bottom: 2px solid #000;
                font-weight: bold;
            }

            .section-header td {
                border-top: 1px solid #000;
                font-weight: bold;
            }

            .total-row td,
            .final-row td {
                font-weight: bold;
            }

            /* Grand total gets border */
            .estimate-table tr:last-child td {
                border-top: 2px solid #000;
                border-bottom: 2px solid #000;
            }

            /* Remove all background colors for print */
            .section-header,
            .subsection-header,
            .total-row,
            .deduction-row,
            .final-row {
                background: white !important;
                color: black !important;
            }

            .print-footer {
                position: fixed;
                bottom: 20px;
                left: 0;
                right: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 20px;
                border-top: 1px solid #000;
                font-size: 12px;
                background: white;
            }

            .print-footer .left {
                text-align: left;
            }

            .print-footer .center {
                text-align: center;
            }

            .print-footer .right {
                text-align: right;
            }
        }

        .print-footer {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DETAILED CUM ABSTRACTED ESTIMATE FOR THE PROPOSED CONSTRUCTION OF A HOUSE</h1>
            <h2>IN R.S.No: <?php echo htmlspecialchars($project['rs_no']); ?> WARD No: <?php echo htmlspecialchars($project['ward_no']); ?> OF <?php echo htmlspecialchars($project['panchayath_municipality']); ?> FOR <?php echo htmlspecialchars($project['client_name']); ?></h2>
            <p><strong>Date: <?php echo date('d/m/Y'); ?></strong></p>
            <button onclick="window.print()" class="print-btn">🖨️ Print Estimate</button>
        </div>

        <div class="project-info">
            <h3>Project Information</h3>
            <p><strong>Project ID:</strong> <?php echo $project['id']; ?></p>
            <p><strong>R.S. No:</strong> <?php echo htmlspecialchars($project['rs_no']); ?></p>
            <p><strong>Client:</strong> <?php echo htmlspecialchars($project['client_name']); ?></p>
            <p><strong>Panchayath/Municipality:</strong> <?php echo htmlspecialchars($project['panchayath_municipality']); ?></p>
            <p><strong>Ground Floor:</strong> <?php echo htmlspecialchars($ground_floor['floor_name'] ?? 'Not found'); ?></p>
        </div>

        <table class="estimate-table">
            <thead>
                <tr>
                    <th>SL.NO.</th>
                    <th>DESCRIPTION</th>
                    <th>NO</th>
                    <th>L</th>
                    <th>B</th>
                    <th>D</th>
                    <th>QTY</th>
                    <th>AMOUNT</th>
                </tr>
            </thead>
            <tbody>
                <!-- 1. Excavation for Foundation -->
                <tr class="section-header">
                    <td colspan="8">1. EXCAVATION FOR FOUNDATION</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Excavation for foundation including labour charges, etc. complete.<br>
                        <strong><?php echo $excavation['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($excavation['amount'], 2); ?></td>
                </tr>

                <?php foreach ($excavation['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 2); ?></td>
                    <td><?php echo number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 3); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Excavation</strong></td>
                    <td><strong><?php echo number_format($excavation['total_qty'], 3); ?> M³</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($excavation['amount'], 2); ?></strong></td>
                </tr>

                <!-- 2. Laterite Masonry for Foundation -->
                <tr class="section-header">
                    <td colspan="8">2. LATERITE MASONRY IN CEMENT MORTAR 1:6 FOR FOUNDATION</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Laterite masonry in cement mortar 1:6 for foundation including cost of materials, conveyance and labour charges, etc. complete.<br>
                        <strong><?php echo $foundation['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($foundation['amount'], 2); ?></td>
                </tr>

                <!-- Below Ground Level -->
                <tr class="subsection-header">
                    <td colspan="8">a. Below Ground Level</td>
                </tr>
                <?php foreach ($foundation['below_gl_items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 2); ?></td>
                    <td><?php echo number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 3); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <!-- Basement -->
                <tr class="subsection-header">
                    <td colspan="8">b. Basement</td>
                </tr>
                <?php foreach ($foundation['basement_items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 2); ?></td>
                    <td><?php echo number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 3); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Foundation Masonry</strong></td>
                    <td><strong><?php echo number_format($foundation['total_qty'], 3); ?> M³</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($foundation['amount'], 2); ?></strong></td>
                </tr>

                <!-- 3. Laterite Masonry for Superstructure -->
                <tr class="section-header">
                    <td colspan="8">3. LATERITE MASONRY IN CEMENT MORTAR 1:6 FOR SUPERSTRUCTURE</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Laterite masonry for superstructure (all floors).<br>
                        <strong>Total <?php echo number_format($final_superstructure_qty, 3); ?> M³ @ Rs.<?php echo $superstructure['rate']; ?> per M³</strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($final_superstructure_amount, 2); ?></td>
                </tr>

                <?php foreach ($superstructure['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 3); ?></td>
                    <td><?php echo number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 3); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Gross Superstructure Masonry</strong></td>
                    <td><strong><?php echo number_format($superstructure['total_qty'], 3); ?> M³</strong></td>
                    <td></td>
                </tr>

                <!-- Deductions for Openings -->
                <tr class="subsection-header">
                    <td colspan="8">Deductions for Doors, Windows, and Openings</td>
                </tr>

                <?php foreach ($deductions['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 3); ?></td>
                    <td><?php echo number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 3); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="deduction-row">
                    <td colspan="6"><strong>Total Deductions</strong></td>
                    <td><strong>-<?php echo number_format($deductions['total_qty'], 3); ?> M³</strong></td>
                    <td></td>
                </tr>

                <tr class="final-row">
                    <td colspan="6"><strong>Net Superstructure Masonry</strong></td>
                    <td><strong><?php echo number_format($final_superstructure_qty, 3); ?> M³</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($final_superstructure_amount, 2); ?></strong></td>
                </tr>

                <!-- 4. Anjilywood Framed Work -->
                <tr class="section-header">
                    <td colspan="8">4. ANJILY WOOD PLANNED AND FRAMED WORK</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Anjily wood planned and framed work for door, window and ventilators including cost and conveyance of all materials, labour charges, etc. complete.<br>
                        <strong><?php echo $anjilywood['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($anjilywood['amount'], 2); ?></td>
                </tr>

                <?php foreach ($anjilywood['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 3); ?></td>
                    <td><?php echo number_format($item['b'], 3); ?></td>
                    <td><?php echo number_format($item['d'], 3); ?></td>
                    <td><?php echo number_format($item['qty'], 6); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Anjilywood Framework</strong></td>
                    <td><strong><?php echo number_format($anjilywood['total_qty_m3'], 6); ?> M³ (<?php echo number_format($anjilywood['total_qty_dm3'], 0); ?> dm³)</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($anjilywood['amount'], 2); ?></strong></td>
                </tr>

                <!-- 5. Anjilywood Door Shutters -->
                <tr class="section-header">
                    <td colspan="8">5. ANJILY WOOD FULLY PANELLED SHUTTERS</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Supplying and fitting Anjily wood fully panelled shutters to suit the door frame already fixed, inclusive of labour charges, conveyance, and cost of all materials, etc. complete.<br>
                        <strong><?php echo $door_shutters['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($door_shutters['amount'], 2); ?></td>
                </tr>

                <?php foreach ($door_shutters['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo number_format($item['l'], 2); ?></td>
                    <td><?php echo number_format($item['b'], 2); ?></td>
                    <td><?php echo $item['d'] === null ? '' : number_format($item['d'], 2); ?></td>
                    <td><?php echo number_format($item['qty'], 4); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Door Shutters</strong></td>
                    <td><strong><?php echo number_format($door_shutters['total_qty_m2'], 4); ?> M² (<?php echo number_format($door_shutters['total_qty_dm2'], 0); ?> dm²)</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($door_shutters['amount'], 2); ?></strong></td>
                </tr>

                <!-- 6. M.S. Grills -->
                <tr class="section-header">
                    <td colspan="8">6. M.S. GRILLS FOR WINDOWS AND VENTILATIONS</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Providing M.S. grills for windows and ventilations, inclusive of the cost of all materials, labour charges, and all other incidental expenses – complete.<br>
                        <strong><?php echo $ms_grills['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($ms_grills['amount'], 2); ?></td>
                </tr>

                <?php foreach ($ms_grills['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d'] === null ? '' : $item['d']; ?></td>
                    <td><?php echo number_format($item['qty'], 2); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total M.S. Grills</strong></td>
                    <td><strong><?php echo number_format($ms_grills['total_qty_kg'], 2); ?> Kg (<?php echo number_format($ms_grills['total_qty_qtl'], 4); ?> Qtl)</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($ms_grills['amount'], 2); ?></strong></td>
                </tr>

                <!-- 7. R.C.C. Work -->
                <tr class="section-header">
                    <td colspan="8">7. R.C.C. WORK 1:1½:3 USING 20MM HARD GRANITE BROKEN STONE</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">R.C.C. Work 1:1½:3 using 20mm hard granite broken stone including cost of all materials and labour charges. Roof, slab etc, are completed.<br>
                        <strong><?php echo $rcc_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($rcc_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($rcc_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo is_numeric($item['l']) ? number_format($item['l'], 3) : $item['l']; ?></td>
                    <td><?php echo is_numeric($item['b']) ? number_format($item['b'], 3) : $item['b']; ?></td>
                    <td><?php echo number_format($item['d'], 3); ?></td>
                    <td><?php echo number_format($item['qty'], 6); ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total R.C.C. Work</strong></td>
                    <td><strong><?php echo number_format($rcc_work['total_qty_m3'], 6); ?> M³ (<?php echo number_format($rcc_work['total_qty_dm3'], 0); ?> dm³)</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($rcc_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 8. CC Work -->
                <tr class="section-header">
                    <td colspan="8">8. CC 1:4:8, USING 40MM BROKEN STONES</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">CC 1:4:8, using 40mm broken stones<br>
                        <strong><?php echo $cc_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($cc_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($cc_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total CC Work</strong></td>
                    <td><strong><?php echo number_format($cc_work['total_qty_m3'], 3); ?> M³</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($cc_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 9. Reinforcement Work -->
                <tr class="section-header">
                    <td colspan="8">9. REINFORCEMENT FOR R.C.C WORK</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Reinforcement for R.C.C work bent, tied and placed in position all cost and conveyance of all materials, labour charges, etc. complete.<br>
                        <strong><?php echo $reinforcement_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($reinforcement_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($reinforcement_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?> Kg</td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Reinforcement</strong></td>
                    <td><strong><?php echo number_format($reinforcement_work['total_qty_kg'], 2); ?> Kg (<?php echo number_format($reinforcement_work['total_qty_qtl'], 4); ?> Qtl)</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($reinforcement_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 10. Plastering Work -->
                <tr class="section-header">
                    <td colspan="8">10. PLASTERING WITH CEMENT MORTAR 1:3 9MM THICK</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Plastering with cement mortar 1:3 9mm thick one coat for ceiling underside of roof slab and sunshade including cost of all materials and labour charges, etc complete.<br>
                        <strong><?php echo $plastering_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($plastering_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($plastering_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Plastering Work</strong></td>
                    <td><strong><?php echo number_format($plastering_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($plastering_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 11. Wall Plastering Work -->
                <tr class="section-header">
                    <td colspan="8">11. PLASTERING WITH C.M. 1:4 12MM THICK</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Plastering with C.M. 1:4 12mm thick for inside and outside walls including cost of all materials and labour charges. Etc.complete.<br>
                        <strong><?php echo $wall_plastering_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($wall_plastering_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($wall_plastering_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Wall Plastering Work</strong></td>
                    <td><strong><?php echo number_format($wall_plastering_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($wall_plastering_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 12. Electrical Work -->
                <tr class="section-header">
                    <td colspan="8">12. ELECTRICAL WORKS</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Electrical works including cost of all materials and labour charges. Etc.complete.<br>
                        <strong><?php echo $electrical_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($electrical_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($electrical_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Electrical Work</strong></td>
                    <td><strong>Lump Sum</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($electrical_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 13. Granite Flooring Work -->
                <tr class="section-header">
                    <td colspan="8">13. FLOORING WITH GRANITE TEL. BLACK SLAB</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Flooring with granite tel. black slab in CM 1:4 12mm thick including cost of all materials and labour charges. Etc. complete<br>
                        <strong><?php echo $granite_flooring_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($granite_flooring_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($granite_flooring_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Granite Flooring Work</strong></td>
                    <td><strong><?php echo number_format($granite_flooring_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($granite_flooring_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 14. Main Flooring Work -->
                <tr class="section-header">
                    <td colspan="8">14. FLOORING WITH GRANITE TEL. BLACK SLAB</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Flooring with granite tel. black slab in CM 1:4 12mm thick including cost of all materials and labour charges. Etc. complete<br>
                        <strong><?php echo $main_flooring_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($main_flooring_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($main_flooring_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Main Flooring Work</strong></td>
                    <td><strong><?php echo number_format($main_flooring_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($main_flooring_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 15. Wall Dado Work -->
                <tr class="section-header">
                    <td colspan="8">15. WALL DADO WITH CERAMIC TILE</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Wall Dado with Ceramic tile in CM 1:4 12mm thick including cost of all materials and labour charges. Etc. complete<br>
                        <strong><?php echo $wall_dado_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($wall_dado_work['amount'], 2); ?></td>
                </tr>

                <?php
                $current_floor = '';
                foreach ($wall_dado_work['items'] as $item):
                    if ($item['floor'] !== $current_floor):
                        $current_floor = $item['floor'];
                ?>
                <tr class="subsection-header">
                    <td colspan="8"><?php echo strtoupper($current_floor); ?></td>
                </tr>
                <?php endif; ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Wall Dado Work</strong></td>
                    <td><strong><?php echo number_format($wall_dado_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($wall_dado_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 16. Painting Work -->
                <tr class="section-header">
                    <td colspan="8">16. PAINTING TWO COATS WITH READY MIXED PLASTIC EMULSION</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Painting two coats with ready mixed plastic emulsion paint approved quality as specified and including priming coat and succeeding coat after rubbing with sand paper and clearing the surface<br>
                        <strong><?php echo $painting_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($painting_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($painting_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Painting Work</strong></td>
                    <td><strong><?php echo number_format($painting_work['total_qty_m2'], 3); ?> M²</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($painting_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 17. Plumbing Work -->
                <tr class="section-header">
                    <td colspan="8">17. PLUMBING WORK</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Plumbing work.<br>
                        <strong><?php echo $plumbing_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($plumbing_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($plumbing_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Plumbing Work</strong></td>
                    <td><strong>Lump Sum</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($plumbing_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- 18. Unforeseen Work -->
                <tr class="section-header">
                    <td colspan="8">18. UNFORSEEN ITEMS & MISC. DEMOLISHING WORKS</td>
                </tr>
                <tr>
                    <td></td>
                    <td class="description">Unforseen items & Misc. demolishing works<br>
                        <strong><?php echo $unforeseen_work['description_summary']; ?></strong>
                    </td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td class="amount">₹<?php echo number_format($unforeseen_work['amount'], 2); ?></td>
                </tr>

                <?php foreach ($unforeseen_work['items'] as $item): ?>
                <tr>
                    <td><?php echo $item['sl_no']; ?></td>
                    <td class="description"><?php echo htmlspecialchars($item['description']); ?></td>
                    <td><?php echo $item['no']; ?></td>
                    <td><?php echo $item['l']; ?></td>
                    <td><?php echo $item['b']; ?></td>
                    <td><?php echo $item['d']; ?></td>
                    <td><?php echo $item['qty']; ?></td>
                    <td></td>
                </tr>
                <?php endforeach; ?>

                <tr class="total-row">
                    <td colspan="6"><strong>Total Unforeseen Work</strong></td>
                    <td><strong>Lump Sum</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($unforeseen_work['amount'], 2); ?></strong></td>
                </tr>

                <!-- Grand Total -->
                <?php
                $grand_total = $excavation['amount'] + $foundation['amount'] + $final_superstructure_amount + $anjilywood['amount'] + $door_shutters['amount'] + $ms_grills['amount'] + $rcc_work['amount'] + $cc_work['amount'] + $reinforcement_work['amount'] + $plastering_work['amount'] + $wall_plastering_work['amount'] + $electrical_work['amount'] + $granite_flooring_work['amount'] + $main_flooring_work['amount'] + $wall_dado_work['amount'] + $painting_work['amount'] + $plumbing_work['amount'] + $unforeseen_work['amount'];
                $grand_total_words = numberToWords(intval($grand_total));
                ?>
                <tr style="background-color: #343a40; color: white; font-weight: bold; font-size: 16px;">
                    <td colspan="7"><strong>GRAND TOTAL</strong></td>
                    <td class="amount"><strong>₹<?php echo number_format($grand_total, 2); ?></strong></td>
                </tr>
                <tr style="background-color: #f8f9fa; font-weight: bold; font-size: 14px;">
                    <td colspan="8" style="text-align: center; padding: 15px;">
                        <strong>Amount in Words: <?php echo $grand_total_words; ?> Rupees Only</strong>
                    </td>
                </tr>
            </tbody>
        </table>

        <div class="nav-links">
            <a href="view_projects.php?project_id=<?php echo $project_id; ?>">← Back to Project Details</a>
            <a href="view_projects.php">All Projects</a>
            <a href="project_settings.php?project_id=<?php echo $project_id; ?>">Project Settings</a>
            <a href="index.php">Dashboard</a>
        </div>

        <!-- Excel Download Button -->
        <div style="text-align: center; margin: 20px 0;" class="no-print">
            <button onclick="downloadExcel()" style="background-color: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;">
                📊 Download Excel
            </button>
        </div>

        <!-- Print Footer -->
        <div class="print-footer">
            <div class="left">
                <strong>Prepared by:</strong><br>
                <br>
                _____________________<br>
                Signature & Date
            </div>
            <div class="center">
                <strong>Page 1/1</strong>
            </div>
            <div class="right">
                <strong>Owned by:</strong><br>
                <br>
                _____________________<br>
                Signature & Date
            </div>
        </div>
    </div>

    <script>
    function downloadExcel() {
        // Create a new window with the estimate data for Excel export
        window.open('estimate_excel.php?project_id=<?php echo $project_id; ?>', '_blank');
    }
    </script>

    <style>
    @media print {
        .no-print {
            display: none !important;
        }
    }
    </style>
</body>
</html>
