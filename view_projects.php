<?php
require_once 'config/database.php';

// Get filter parameters
$filter_panchayath = $_GET['filter_panchayath'] ?? '';
$filter_client = $_GET['filter_client'] ?? '';
$selected_project_id = $_GET['project_id'] ?? '';

// Handle success/error messages
$success_message = '';
$error_message = '';

if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'project_deleted':
            $success_message = 'Project deleted successfully!';
            break;
    }
}

if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'invalid_id':
            $error_message = 'Invalid project ID provided.';
            break;
        case 'project_not_found':
            $error_message = 'Project not found.';
            break;
    }
}

try {
    $pdo = getDatabaseConnection();
    
    // Build query for projects list
    $where_conditions = [];
    $params = [];
    
    if (!empty($filter_panchayath)) {
        $where_conditions[] = "panchayath_municipality LIKE ?";
        $params[] = "%$filter_panchayath%";
    }
    
    if (!empty($filter_client)) {
        $where_conditions[] = "client_name LIKE ?";
        $params[] = "%$filter_client%";
    }
    
    $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    // Get projects
    $projects_query = "
        SELECT id, rs_no, ward_no, panchayath_municipality, client_name, 
               dxf_filename, processing_status, upload_date, created_at
        FROM projects 
        $where_clause 
        ORDER BY created_at DESC
    ";
    
    $stmt = $pdo->prepare($projects_query);
    $stmt->execute($params);
    $projects = $stmt->fetchAll();
    
    // Get project details if a specific project is selected
    $project_details = null;
    if ($selected_project_id) {
        $project_details = getProjectDetails($pdo, $selected_project_id);
    }
    
    // Get unique panchayaths for filter dropdown
    $panchayath_stmt = $pdo->query("SELECT DISTINCT panchayath_municipality FROM projects ORDER BY panchayath_municipality");
    $panchayaths = $panchayath_stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    $error_message = "Database error: " . $e->getMessage();
}

function getProjectDetails($pdo, $project_id) {
    $details = [];
    
    // Get project info
    $stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$project_id]);
    $details['project'] = $stmt->fetch();
    
    if (!$details['project']) {
        return null;
    }
    
    // Get floors
    $stmt = $pdo->prepare("SELECT * FROM floors WHERE project_id = ? ORDER BY floor_name");
    $stmt->execute([$project_id]);
    $details['floors'] = $stmt->fetchAll();
    
    // Get walls
    $stmt = $pdo->prepare("
        SELECT w.*, f.floor_name 
        FROM walls w 
        LEFT JOIN floors f ON w.floor_id = f.id 
        WHERE w.project_id = ? 
        ORDER BY f.floor_name, w.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['walls'] = $stmt->fetchAll();
    
    // Get doors
    $stmt = $pdo->prepare("
        SELECT d.*, f.floor_name 
        FROM doors d 
        LEFT JOIN floors f ON d.floor_id = f.id 
        WHERE d.project_id = ? 
        ORDER BY f.floor_name, d.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['doors'] = $stmt->fetchAll();
    
    // Get windows
    $stmt = $pdo->prepare("
        SELECT w.*, f.floor_name 
        FROM windows w 
        LEFT JOIN floors f ON w.floor_id = f.id 
        WHERE w.project_id = ? 
        ORDER BY f.floor_name, w.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['windows'] = $stmt->fetchAll();
    
    // Get openings
    $stmt = $pdo->prepare("
        SELECT o.*, f.floor_name 
        FROM openings o 
        LEFT JOIN floors f ON o.floor_id = f.id 
        WHERE o.project_id = ? 
        ORDER BY f.floor_name, o.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['openings'] = $stmt->fetchAll();
    
    // Get slabs
    $stmt = $pdo->prepare("
        SELECT s.*, f.floor_name 
        FROM slabs s 
        LEFT JOIN floors f ON s.floor_id = f.id 
        WHERE s.project_id = ? 
        ORDER BY f.floor_name, s.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['slabs'] = $stmt->fetchAll();
    
    // Get staircases
    $stmt = $pdo->prepare("
        SELECT s.*, f.floor_name 
        FROM staircases s 
        LEFT JOIN floors f ON s.floor_id = f.id 
        WHERE s.project_id = ? 
        ORDER BY f.floor_name
    ");
    $stmt->execute([$project_id]);
    $details['staircases'] = $stmt->fetchAll();
    
    // Get columns
    $stmt = $pdo->prepare("
        SELECT c.*, f.floor_name 
        FROM columns c 
        LEFT JOIN floors f ON c.floor_id = f.id 
        WHERE c.project_id = ? 
        ORDER BY f.floor_name, c.length, c.width
    ");
    $stmt->execute([$project_id]);
    $details['columns'] = $stmt->fetchAll();
    
    // Get beams
    $stmt = $pdo->prepare("
        SELECT b.*, f.floor_name 
        FROM beams b 
        LEFT JOIN floors f ON b.floor_id = f.id 
        WHERE b.project_id = ? 
        ORDER BY f.floor_name, b.length, b.width
    ");
    $stmt->execute([$project_id]);
    $details['beams'] = $stmt->fetchAll();
    
    // Get flooring
    $stmt = $pdo->prepare("
        SELECT fl.*, f.floor_name 
        FROM flooring fl 
        LEFT JOIN floors f ON fl.floor_id = f.id 
        WHERE fl.project_id = ? 
        ORDER BY f.floor_name, fl.layer_name
    ");
    $stmt->execute([$project_id]);
    $details['flooring'] = $stmt->fetchAll();
    
    // Get footprint
    $stmt = $pdo->prepare("
        SELECT fp.*, f.floor_name 
        FROM footprint fp 
        LEFT JOIN floors f ON fp.floor_id = f.id 
        WHERE fp.project_id = ? 
        ORDER BY f.floor_name
    ");
    $stmt->execute([$project_id]);
    $details['footprint'] = $stmt->fetchAll();
    
    return $details;
}

function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge badge-warning">Pending</span>',
        'processing' => '<span class="badge badge-info">Processing</span>',
        'completed' => '<span class="badge badge-success">Completed</span>',
        'error' => '<span class="badge badge-danger">Error</span>'
    ];
    return $badges[$status] ?? '<span class="badge badge-secondary">Unknown</span>';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Projects - SmartEstimate 3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }
        
        .filter-group {
            flex: 1;
            min-width: 200px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }

        .btn-danger {
            background-color: #dc3545;
            margin-left: 5px;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .projects-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .projects-table th,
        .projects-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .projects-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .projects-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .badge-warning { background-color: #ffc107; color: #212529; }
        .badge-info { background-color: #17a2b8; color: white; }
        .badge-success { background-color: #28a745; color: white; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-secondary { background-color: #6c757d; color: white; }
        
        .project-details {
            margin-top: 30px;
            border-top: 2px solid #007bff;
            padding-top: 30px;
        }
        
        .details-section {
            margin-bottom: 30px;
        }
        
        .details-section h3 {
            color: #007bff;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .details-table th,
        .details-table td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
            font-size: 14px;
        }
        
        .details-table th {
            background-color: #f8f9fa;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>View Projects</h1>

        <?php if ($error_message): ?>
            <div class="error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filters">
            <form method="GET">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="filter_panchayath">Filter by Panchayath/Municipality</label>
                        <select id="filter_panchayath" name="filter_panchayath">
                            <option value="">All Panchayaths</option>
                            <?php foreach ($panchayaths as $panchayath): ?>
                                <option value="<?php echo htmlspecialchars($panchayath); ?>"
                                        <?php echo $filter_panchayath === $panchayath ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($panchayath); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="filter_client">Filter by Client Name</label>
                        <input type="text" id="filter_client" name="filter_client"
                               value="<?php echo htmlspecialchars($filter_client); ?>"
                               placeholder="Enter client name">
                    </div>

                    <div class="filter-group">
                        <button type="submit" class="btn">Apply Filters</button>
                        <a href="view_projects.php" class="btn btn-secondary">Clear</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Projects Table -->
        <table class="projects-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>R.S. No</th>
                    <th>Ward No</th>
                    <th>Panchayath/Municipality</th>
                    <th>Client Name</th>
                    <th>Status</th>
                    <th>Upload Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($projects)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 30px; color: #666;">
                            No projects found
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($projects as $project): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($project['id']); ?></td>
                            <td><?php echo htmlspecialchars($project['rs_no']); ?></td>
                            <td><?php echo htmlspecialchars($project['ward_no']); ?></td>
                            <td><?php echo htmlspecialchars($project['panchayath_municipality']); ?></td>
                            <td><?php echo htmlspecialchars($project['client_name']); ?></td>
                            <td><?php echo getStatusBadge($project['processing_status']); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($project['upload_date'])); ?></td>
                            <td>
                                <?php
                                // Build query parameters excluding project_id to avoid duplication
                                $query_params = $_GET;
                                unset($query_params['project_id']);
                                $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
                                ?>
                                <a href="?project_id=<?php echo $project['id']; ?><?php echo $query_string; ?>"
                                   class="btn">View Details</a>
                                <a href="project_settings.php?project_id=<?php echo $project['id']; ?>"
                                   class="btn" style="background-color: #28a745; margin-left: 5px;">Settings</a>
                                <a href="delete_project.php?id=<?php echo $project['id']; ?>"
                                   class="btn btn-danger">Delete</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <!-- Project Details -->
        <?php if ($project_details): ?>
            <div class="project-details">
                <h2>Project Details: <?php echo htmlspecialchars($project_details['project']['client_name']); ?></h2>

                <!-- Project Info -->
                <div class="details-section">
                    <h3>Project Information</h3>
                    <table class="details-table">
                        <tr><th>R.S. No</th><td><?php echo htmlspecialchars($project_details['project']['rs_no']); ?></td></tr>
                        <tr><th>Ward No</th><td><?php echo htmlspecialchars($project_details['project']['ward_no']); ?></td></tr>
                        <tr><th>Panchayath/Municipality</th><td><?php echo htmlspecialchars($project_details['project']['panchayath_municipality']); ?></td></tr>
                        <tr><th>Client Name</th><td><?php echo htmlspecialchars($project_details['project']['client_name']); ?></td></tr>
                        <tr><th>DXF File</th><td><?php echo htmlspecialchars($project_details['project']['dxf_filename']); ?></td></tr>
                        <tr><th>Status</th><td><?php echo getStatusBadge($project_details['project']['processing_status']); ?></td></tr>
                        <tr><th>Upload Date</th><td><?php echo date('Y-m-d H:i:s', strtotime($project_details['project']['upload_date'])); ?></td></tr>
                    </table>
                </div>

                <!-- Floors -->
                <?php if (!empty($project_details['floors'])): ?>
                    <div class="details-section">
                        <h3>Floors</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor Name</th>
                                    <th>Boundary Area (sq.m)</th>
                                    <th>Coordinates</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['floors'] as $floor): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($floor['floor_name']); ?></td>
                                        <td><?php echo number_format($floor['boundary_area'], 2); ?></td>
                                        <td>
                                            (<?php echo number_format($floor['min_x'], 2); ?>, <?php echo number_format($floor['min_y'], 2); ?>) to
                                            (<?php echo number_format($floor['max_x'], 2); ?>, <?php echo number_format($floor['max_y'], 2); ?>)
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Walls -->
                <?php if (!empty($project_details['walls'])): ?>
                    <div class="details-section">
                        <h3>Walls</h3>
                        <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
                            <em>Note: Length represents the longer side of the wall rectangle, regardless of orientation.</em>
                        </p>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Thickness (m)</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['walls'] as $wall): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($wall['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($wall['layer_name']); ?></td>
                                        <td><?php echo number_format($wall['thickness'], 3); ?></td>
                                        <td><?php echo number_format($wall['length'], 2); ?></td>
                                        <td><?php echo number_format($wall['width'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Doors -->
                <?php if (!empty($project_details['doors'])): ?>
                    <div class="details-section">
                        <h3>Doors</h3>
                        <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
                            <em>Note: Length represents the longer side of the door rectangle, regardless of orientation.</em>
                        </p>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Count</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                    <th>Outer Side</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['doors'] as $door): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($door['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($door['layer_name']); ?></td>
                                        <td><?php echo $door['count']; ?></td>
                                        <td><?php echo number_format($door['length'], 3); ?></td>
                                        <td><?php echo number_format($door['width'], 3); ?></td>
                                        <td style="text-align: center;">
                                            <?php if ($door['outer_side']): ?>
                                                <span style="color: #28a745; font-weight: bold;">✅ Yes</span>
                                            <?php else: ?>
                                                <span style="color: #6c757d;">❌ No</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Windows -->
                <?php if (!empty($project_details['windows'])): ?>
                    <div class="details-section">
                        <h3>Windows</h3>
                        <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
                            <em>Note: Length represents the longer side of the window rectangle, regardless of orientation.</em>
                        </p>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Count</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                    <th>Outer Side</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['windows'] as $window): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($window['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($window['layer_name']); ?></td>
                                        <td><?php echo $window['count']; ?></td>
                                        <td><?php echo number_format($window['length'], 3); ?></td>
                                        <td><?php echo number_format($window['width'], 3); ?></td>
                                        <td style="text-align: center;">
                                            <?php if ($window['outer_side']): ?>
                                                <span style="color: #28a745; font-weight: bold;">✅ Yes</span>
                                            <?php else: ?>
                                                <span style="color: #6c757d;">❌ No</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Openings -->
                <?php if (!empty($project_details['openings'])): ?>
                    <div class="details-section">
                        <h3>Openings</h3>
                        <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
                            <em>Note: Length represents the longer side of the opening rectangle, regardless of orientation.</em>
                        </p>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Count</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                    <th>Outer Side</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['openings'] as $opening): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($opening['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($opening['layer_name']); ?></td>
                                        <td><?php echo $opening['count']; ?></td>
                                        <td><?php echo number_format($opening['length'], 3); ?></td>
                                        <td><?php echo number_format($opening['width'], 3); ?></td>
                                        <td style="text-align: center;">
                                            <?php if ($opening['outer_side']): ?>
                                                <span style="color: #28a745; font-weight: bold;">✅ Yes</span>
                                            <?php else: ?>
                                                <span style="color: #6c757d;">❌ No</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Columns -->
                <?php if (!empty($project_details['columns'])): ?>
                    <div class="details-section">
                        <h3>Columns</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Size (L×W)</th>
                                    <th>Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['columns'] as $column): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($column['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo number_format($column['length'], 2) . '×' . number_format($column['width'], 2); ?> m</td>
                                        <td><?php echo $column['count']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Slabs -->
                <?php if (!empty($project_details['slabs'])): ?>
                    <div class="details-section">
                        <h3>Slabs</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Area (sq.m)</th>
                                    <th>Perimeter (m)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['slabs'] as $slab): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($slab['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($slab['layer_name']); ?></td>
                                        <td><?php echo number_format($slab['area'], 2); ?></td>
                                        <td><?php echo number_format($slab['perimeter'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Staircases -->
                <?php if (!empty($project_details['staircases'])): ?>
                    <div class="details-section">
                        <h3>Staircases</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Count</th>
                                    <th>Total Area (sq.m)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['staircases'] as $staircase): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($staircase['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo $staircase['count']; ?></td>
                                        <td><?php echo number_format($staircase['area'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Beams -->
                <?php if (!empty($project_details['beams'])): ?>
                    <div class="details-section">
                        <h3>Beams</h3>
                        <p style="font-size: 14px; color: #666; margin-bottom: 10px;">
                            <em>Note: Length represents the longer side of the beam rectangle, regardless of orientation.</em>
                        </p>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                    <th>Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['beams'] as $beam): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($beam['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo number_format($beam['length'], 3); ?></td>
                                        <td><?php echo number_format($beam['width'], 3); ?></td>
                                        <td><?php echo $beam['count']; ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Flooring -->
                <?php if (!empty($project_details['flooring'])): ?>
                    <div class="details-section">
                        <h3>Flooring</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Layer</th>
                                    <th>Length (m)</th>
                                    <th>Width (m)</th>
                                    <th>Area (sq.m)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['flooring'] as $floor): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($floor['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo htmlspecialchars($floor['layer_name']); ?></td>
                                        <td><?php echo number_format($floor['length'], 3); ?></td>
                                        <td><?php echo number_format($floor['width'], 3); ?></td>
                                        <td><?php echo number_format($floor['area'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>

                <!-- Footprint -->
                <?php if (!empty($project_details['footprint'])): ?>
                    <div class="details-section">
                        <h3>Footprint</h3>
                        <table class="details-table">
                            <thead>
                                <tr>
                                    <th>Floor</th>
                                    <th>Perimeter (m)</th>
                                    <th>Area (sq.m)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($project_details['footprint'] as $fp): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($fp['floor_name'] ?? 'Unknown'); ?></td>
                                        <td><?php echo number_format($fp['perimeter'], 2); ?></td>
                                        <td><?php echo number_format($fp['area'], 2); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <div class="nav-links">
            <a href="add_project.php">Add New Project</a>
        </div>
    </div>
</body>
</html>
