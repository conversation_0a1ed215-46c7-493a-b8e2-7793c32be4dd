# SmartEstimate 3.0 - DXF Processing System

A comprehensive system for processing DXF files and extracting architectural data for construction estimation.

## Features

- **DXF File Processing**: Parse DXF files and extract architectural elements
- **Layer-Based Extraction**: Extract data based on specific layer naming conventions
- **Floor Identification**: Automatically identify and categorize floors
- **Comprehensive Data Extraction**:
  - Wall calculations with thickness-based length computation
  - Door, window, and opening schedules
  - Slab areas and perimeters
  - Staircase counts
  - Column sizes and counts
  - Beam schedules
  - Flooring areas
  - Footprint perimeters
- **Web Interface**: User-friendly PHP interface for project management
- **Database Storage**: MySQL database for structured data storage
- **Filtering and Search**: Advanced filtering by Panchayath/Municipality and Client Name

## System Requirements

- **PHP**: 7.4 or higher with PDO MySQL extension
- **Python**: 3.7 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache, Nginx, or PHP built-in server

## Installation

1. **Clone or download the project files**

2. **Run the setup script**:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Configure the database**:
   - Update database credentials in `config/database.php`
   - Import the database schema:
     ```bash
     mysql -u root -p < database_schema.sql
     ```

4. **Install Python dependencies**:
   ```bash
   pip3 install -r requirements.txt
   ```

5. **Configure web server** or use PHP built-in server:
   ```bash
   php -S localhost:8000
   ```

## Usage

### Adding a New Project

1. Navigate to `add_project.php`
2. Fill in the project details:
   - R.S. No (e.g., 116/3A1A2PT3)
   - Ward No (e.g., 3)
   - Panchayath/Municipality (select from dropdown)
   - Client Name (e.g., JOHN DOE)
   - Upload DXF file
3. Submit the form
4. The system will process the DXF file automatically

### Viewing Projects

1. Navigate to `view_projects.php`
2. Use filters to search by Panchayath/Municipality or Client Name
3. Click "View Details" to see extracted data for any project

## DXF Layer Conventions

The system expects the following layer naming conventions:

### Floor Layers
- `GROUND_FLOOR`
- `FIRST_FLOOR`
- `SECOND_FLOOR`
- `THIRD_FLOOR`

### Wall Layers
- `WALLS_10CM` (0.1m thickness)
- `WALLS_20CM` (0.2m thickness)
- `WALLS_23CM` (0.23m thickness)

### Door Layers
- `DOOR_D`
- `DOOR_D1`
- `DOOR_D2`
- `DOOR_MD`

### Window Layers
- `WINDOW_KW3`
- `WINDOW_V1`
- `WINDOW_W1`
- `WINDOW_W2`
- `WINDOW_W3`
- `WINDOW_W4`

### Other Layers
- `OPENINGS` - General openings
- `MAIN_SLAB_TOTAL` - Main slab areas
- `MAIN_SLAB_DEDUCTION` - Slab deductions
- `STAIRCASE` - Staircase elements
- `RCC_COLUMN` - RCC columns
- `RCC_BEAM` - RCC beams
- `FLOORING_KITCHEN` - Kitchen flooring
- `FLOORING_MAIN` - Main area flooring
- `FLOORING_TOILET` - Toilet flooring
- `FOOTPRINT` - Building footprint

## Database Schema

The system uses the following main tables:

- `projects` - Project information
- `floors` - Floor boundaries and areas
- `walls` - Wall data with thickness and length calculations
- `doors` - Door schedule
- `windows` - Window schedule
- `openings` - Opening schedule
- `slabs` - Slab areas
- `staircases` - Staircase counts
- `columns` - Column sizes and counts
- `beams` - Beam schedule
- `flooring` - Flooring areas
- `footprint` - Building footprint data

## File Structure

```
SmartEstimate 3.0/
├── add_project.php          # Project creation form
├── view_projects.php        # Project viewer and data display
├── dxf_parser.py           # Python DXF processing script
├── config/
│   └── database.php        # Database configuration
├── uploads/
│   └── dxf_files/         # Uploaded DXF files
├── database_schema.sql     # MySQL database schema
├── requirements.txt        # Python dependencies
├── setup.sh               # Setup script
└── README.md              # This file
```

## Troubleshooting

### Common Issues

1. **File upload errors**: Check PHP upload limits in `php.ini`
2. **Database connection errors**: Verify credentials in `config/database.php`
3. **Python script errors**: Check Python dependencies and file permissions
4. **DXF parsing errors**: Ensure DXF file follows expected layer conventions

### Logs

- Check web server error logs for PHP issues
- Python script outputs are logged during execution

## Security Considerations

- File uploads are restricted to DXF files only
- All database queries use prepared statements
- File paths are validated and sanitized
- Upload directory is outside web root when properly configured

## Future Enhancements

- Support for additional DXF layer types
- Export functionality (PDF, Excel)
- Project editing and deletion
- User authentication and authorization
- API endpoints for integration
- Advanced reporting and analytics

## Support

For technical support or feature requests, please refer to the project documentation or contact the development team.
