#!/usr/bin/env python3
"""
SmartEstimate 3.0 DXF Parser
Parses DXF files and extracts architectural data based on layer-specific logic
"""

import sys
import argparse
import json
import mysql.connector
from mysql.connector import Error
import ezdxf
from ezdxf.math import Vec2
from shapely.geometry import Polygon, Point
import numpy as np
from typing import List, Dict, Tuple, Optional

class DXFParser:
    def __init__(self, db_config: Dict[str, str]):
        self.db_config = db_config
        self.connection = None
        self.project_id = None
        self.floors = {}  # floor_name -> floor_data
        
        # Layer definitions
        self.floor_layers = ['GROUND_FLOOR', 'FIRST_FLOOR', 'SECOND_FLOOR', 'THIRD_FLOOR']
        self.wall_layers = {
            'WALLS_10CM': 0.1,
            'WALLS_20CM': 0.2,
            'WALLS_23CM': 0.23
        }
        self.door_layers = ['DOOR_D', 'DOOR_D1', 'DOOR_D2', 'DOOR_MD']
        self.window_layers = ['WINDOW_KW3', 'WINDOW_V1', 'WINDOW_W1', 'WINDOW_W2', 'WINDOW_W3', 'WINDOW_W4']
        self.opening_layers = ['OPENINGS']
        self.slab_layers = ['MAIN_SLAB_TOTAL', 'MAIN_SLAB_DEDUCTION']
        self.staircase_layers = ['STAIRCASE']
        self.column_layers = ['RCC_COLUMN']
        self.beam_layers = ['RCC_BEAM']
        self.flooring_layers = ['FLOORING_KITCHEN', 'FLOORING_MAIN', 'FLOORING_TOILET']
        self.footprint_layers = ['FOOTPRINT']

    def connect_database(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                print("Successfully connected to MySQL database")
                return True
        except Error as e:
            print(f"Error connecting to MySQL: {e}")
            return False

    def close_database(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("MySQL connection closed")

    def parse_dxf_file(self, dxf_path: str, project_id: int) -> bool:
        """Main function to parse DXF file and extract data"""
        try:
            self.project_id = project_id

            # Load DXF document
            doc = ezdxf.readfile(dxf_path)
            msp = doc.modelspace()

            available_layers = [layer.dxf.name for layer in doc.layers]
            print(f"Parsing DXF file: {dxf_path}")
            print(f"Available layers: {available_layers}")

            # Track extraction results
            extraction_summary = {
                'floors': 0, 'walls': 0, 'doors': 0, 'windows': 0, 'openings': 0,
                'slabs': 0, 'staircases': 0, 'columns': 0, 'beams': 0, 'flooring': 0, 'footprint': 0
            }

            # Step 1: Identify floors
            extraction_summary['floors'] = self.extract_floors(msp)

            # Step 2: Extract walls
            extraction_summary['walls'] = self.extract_walls(msp)

            # Step 3: Extract doors, windows, openings
            extraction_summary['doors'] = self.extract_doors(msp)
            extraction_summary['windows'] = self.extract_windows(msp)
            extraction_summary['openings'] = self.extract_openings(msp)

            # Step 4: Extract slabs
            extraction_summary['slabs'] = self.extract_slabs(msp)

            # Step 5: Extract staircases
            extraction_summary['staircases'] = self.extract_staircases(msp)

            # Step 6: Extract columns
            extraction_summary['columns'] = self.extract_columns(msp)

            # Step 7: Extract beams
            extraction_summary['beams'] = self.extract_beams(msp)

            # Step 8: Extract flooring
            extraction_summary['flooring'] = self.extract_flooring(msp)

            # Step 9: Extract footprint
            extraction_summary['footprint'] = self.extract_footprint(msp)

            # Print extraction summary
            print("\n=== EXTRACTION SUMMARY ===")
            for category, count in extraction_summary.items():
                print(f"{category.upper()}: {count} items extracted")

            # Check for missing layers
            self.check_missing_layers(available_layers)

            # Update project status
            self.update_project_status('completed')

            print("DXF parsing completed successfully")
            return True

        except Exception as e:
            print(f"Error parsing DXF file: {e}")
            self.update_project_status('error')
            return False

    def get_rectangles_from_layer(self, msp, layer_name: str) -> List[Dict]:
        """Extract rectangles/polylines from a specific layer"""
        rectangles = []

        for entity in msp.query(f'*[layer=="{layer_name}"]'):
            if entity.dxftype() == 'LWPOLYLINE' or entity.dxftype() == 'POLYLINE':
                points = list(entity.get_points())
                if len(points) >= 4:  # At least 4 points for a rectangle
                    # Calculate bounding box
                    x_coords = [p[0] for p in points]
                    y_coords = [p[1] for p in points]

                    min_x, max_x = min(x_coords), max(x_coords)
                    min_y, max_y = min(y_coords), max(y_coords)

                    width = max_x - min_x
                    height = max_y - min_y
                    area = width * height

                    # Calculate actual polyline length
                    polyline_length = 0.0
                    for i in range(len(points)):
                        p1 = points[i]
                        p2 = points[(i + 1) % len(points)]  # Wrap around to first point
                        segment_length = ((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)**0.5
                        polyline_length += segment_length

                    rectangles.append({
                        'points': points,
                        'min_x': min_x,
                        'min_y': min_y,
                        'max_x': max_x,
                        'max_y': max_y,
                        'width': width,
                        'height': height,
                        'area': area,
                        'polyline_length': polyline_length,
                        'center_x': (min_x + max_x) / 2,
                        'center_y': (min_y + max_y) / 2
                    })

        return rectangles

    def point_in_rectangle(self, point_x: float, point_y: float, rect: Dict) -> bool:
        """Check if a point is inside a rectangle"""
        return (rect['min_x'] <= point_x <= rect['max_x'] and 
                rect['min_y'] <= point_y <= rect['max_y'])

    def find_floor_for_point(self, x: float, y: float) -> Optional[int]:
        """Find which floor a point belongs to"""
        for floor_name, floor_data in self.floors.items():
            if self.point_in_rectangle(x, y, floor_data):
                return floor_data['id']
        return None

    def extract_floors(self, msp):
        """Extract floor boundaries"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.floor_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No floor boundaries found in layer: {layer_name}")
                continue

            for rect in rectangles:
                # Insert floor into database
                query = """
                INSERT INTO floors (project_id, floor_name, boundary_area, min_x, min_y, max_x, max_y)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, layer_name, rect['area'],
                    rect['min_x'], rect['min_y'], rect['max_x'], rect['max_y']
                )

                cursor.execute(query, values)
                floor_id = cursor.lastrowid

                # Store floor data for reference
                rect['id'] = floor_id
                self.floors[layer_name] = rect

                print(f"Extracted floor: {layer_name}, Area: {rect['area']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_walls(self, msp):
        """Extract wall data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name, thickness in self.wall_layers.items():
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No walls found in layer: {layer_name}")
                continue

            for rect in rectangles:
                # Store only length and width dimensions
                wall_length = rect['width']  # Length of the wall
                wall_width = rect['height']  # Width of the wall
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                # Insert wall into database (area set to NULL since not needed)
                query = """
                INSERT INTO walls (project_id, floor_id, layer_name, thickness, area, length, x, y, width, height)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, thickness, None,
                    wall_length, rect['center_x'], rect['center_y'], wall_width, thickness
                )

                cursor.execute(query, values)
                print(f"Extracted wall: {layer_name}, Length: {wall_length:.2f}m, Width: {wall_width:.2f}m, Floor: {floor_id}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_doors(self, msp):
        """Extract door data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.door_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No doors found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                query = """
                INSERT INTO doors (project_id, floor_id, layer_name, count, length, width, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, 1,
                    rect['width'], rect['height'], rect['center_x'], rect['center_y']
                )

                cursor.execute(query, values)
                print(f"Extracted door: {layer_name}, Size: {rect['width']:.2f}x{rect['height']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_windows(self, msp):
        """Extract window data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.window_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No windows found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                query = """
                INSERT INTO windows (project_id, floor_id, layer_name, count, length, width, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, 1,
                    rect['width'], rect['height'], rect['center_x'], rect['center_y']
                )

                cursor.execute(query, values)
                print(f"Extracted window: {layer_name}, Size: {rect['width']:.2f}x{rect['height']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_openings(self, msp):
        """Extract opening data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.opening_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No openings found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                query = """
                INSERT INTO openings (project_id, floor_id, layer_name, count, length, width, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, 1,
                    rect['width'], rect['height'], rect['center_x'], rect['center_y']
                )

                cursor.execute(query, values)
                print(f"Extracted opening: {layer_name}, Size: {rect['width']:.2f}x{rect['height']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_slabs(self, msp):
        """Extract slab data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.slab_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No slabs found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])
                perimeter = 2 * (rect['width'] + rect['height'])

                query = """
                INSERT INTO slabs (project_id, floor_id, layer_name, area, perimeter)
                VALUES (%s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, rect['area'], perimeter
                )

                cursor.execute(query, values)
                print(f"Extracted slab: {layer_name}, Area: {rect['area']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_staircases(self, msp):
        """Extract staircase data"""
        cursor = self.connection.cursor()
        total_count = 0

        for layer_name in self.staircase_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No staircases found in layer: {layer_name}")
                continue

            # Count staircases per floor
            floor_counts = {}
            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])
                if floor_id not in floor_counts:
                    floor_counts[floor_id] = {'count': 0, 'total_area': 0}
                floor_counts[floor_id]['count'] += 1
                floor_counts[floor_id]['total_area'] += rect['area']
                total_count += 1

            # Insert staircase data
            for floor_id, data in floor_counts.items():
                query = """
                INSERT INTO staircases (project_id, floor_id, count, area)
                VALUES (%s, %s, %s, %s)
                """
                values = (self.project_id, floor_id, data['count'], data['total_area'])

                cursor.execute(query, values)
                print(f"Extracted staircases: Floor {floor_id}, Count: {data['count']}")

        self.connection.commit()
        cursor.close()
        return total_count

    def extract_columns(self, msp):
        """Extract column data"""
        cursor = self.connection.cursor()
        total_count = 0

        for layer_name in self.column_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No columns found in layer: {layer_name}")
                continue

            # Group columns by size and floor
            column_groups = {}
            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])
                size_key = f"{rect['width']:.3f}x{rect['height']:.3f}"
                group_key = f"{floor_id}_{size_key}"

                if group_key not in column_groups:
                    column_groups[group_key] = {
                        'floor_id': floor_id,
                        'length': rect['width'],
                        'width': rect['height'],
                        'count': 0,
                        'x': rect['center_x'],
                        'y': rect['center_y']
                    }
                column_groups[group_key]['count'] += 1
                total_count += 1

            # Insert column data
            for group_data in column_groups.values():
                query = """
                INSERT INTO columns (project_id, floor_id, length, width, count, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, group_data['floor_id'], group_data['length'],
                    group_data['width'], group_data['count'], group_data['x'], group_data['y']
                )

                cursor.execute(query, values)
                print(f"Extracted columns: {group_data['length']:.2f}x{group_data['width']:.2f}, Count: {group_data['count']}")

        self.connection.commit()
        cursor.close()
        return total_count

    def extract_beams(self, msp):
        """Extract beam data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.beam_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No beams found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                query = """
                INSERT INTO beams (project_id, floor_id, length, width, count, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, rect['width'], rect['height'], 1,
                    rect['center_x'], rect['center_y']
                )

                cursor.execute(query, values)
                print(f"Extracted beam: {rect['width']:.2f}x{rect['height']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_flooring(self, msp):
        """Extract flooring data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.flooring_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No flooring found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])

                query = """
                INSERT INTO flooring (project_id, floor_id, layer_name, length, width, area, x, y)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = (
                    self.project_id, floor_id, layer_name, rect['width'], rect['height'],
                    rect['area'], rect['center_x'], rect['center_y']
                )

                cursor.execute(query, values)
                print(f"Extracted flooring: {layer_name}, Area: {rect['area']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def extract_footprint(self, msp):
        """Extract footprint data"""
        cursor = self.connection.cursor()
        count = 0

        for layer_name in self.footprint_layers:
            rectangles = self.get_rectangles_from_layer(msp, layer_name)

            if not rectangles:
                print(f"No footprint found in layer: {layer_name}")
                continue

            for rect in rectangles:
                floor_id = self.find_floor_for_point(rect['center_x'], rect['center_y'])
                perimeter = 2 * (rect['width'] + rect['height'])

                query = """
                INSERT INTO footprint (project_id, floor_id, perimeter, area)
                VALUES (%s, %s, %s, %s)
                """
                values = (self.project_id, floor_id, perimeter, rect['area'])

                cursor.execute(query, values)
                print(f"Extracted footprint: Perimeter: {perimeter:.2f}, Area: {rect['area']:.2f}")
                count += 1

        self.connection.commit()
        cursor.close()
        return count

    def check_missing_layers(self, available_layers: List[str]):
        """Check for missing or empty layers and report them"""
        print("\n=== LAYER ANALYSIS ===")

        all_expected_layers = (
            self.floor_layers +
            list(self.wall_layers.keys()) +
            self.door_layers +
            self.window_layers +
            self.opening_layers +
            self.slab_layers +
            self.staircase_layers +
            self.column_layers +
            self.beam_layers +
            self.flooring_layers +
            self.footprint_layers
        )

        missing_layers = []
        empty_layers = []

        for layer in all_expected_layers:
            if layer not in available_layers:
                missing_layers.append(layer)
            else:
                # Check if layer has any objects
                # This would require checking the DXF document, for now we'll just report available
                pass

        if missing_layers:
            print(f"MISSING LAYERS: {', '.join(missing_layers)}")
        else:
            print("All expected layers are present in the DXF file")

        print(f"TOTAL LAYERS FOUND: {len(available_layers)}")
        print(f"EXPECTED LAYERS: {len(all_expected_layers)}")

    def update_project_status(self, status: str):
        """Update project processing status"""
        cursor = self.connection.cursor()
        query = "UPDATE projects SET processing_status = %s WHERE id = %s"
        cursor.execute(query, (status, self.project_id))
        self.connection.commit()
        cursor.close()


def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description='Parse DXF file and extract architectural data')
    parser.add_argument('dxf_file', help='Path to DXF file')
    parser.add_argument('project_id', type=int, help='Project ID from database')
    parser.add_argument('--host', default='localhost', help='Database host')
    parser.add_argument('--database', default='smartestimate_db', help='Database name')
    parser.add_argument('--user', default='root', help='Database user')
    parser.add_argument('--password', default='', help='Database password')

    args = parser.parse_args()

    # Database configuration
    db_config = {
        'host': args.host,
        'database': args.database,
        'user': args.user,
        'password': args.password,
        'charset': 'utf8mb4',
        'autocommit': False
    }

    # Create parser instance
    dxf_parser = DXFParser(db_config)

    try:
        # Connect to database
        if not dxf_parser.connect_database():
            print("Failed to connect to database")
            sys.exit(1)

        # Parse DXF file
        success = dxf_parser.parse_dxf_file(args.dxf_file, args.project_id)

        if success:
            print("DXF parsing completed successfully")
            sys.exit(0)
        else:
            print("DXF parsing failed")
            sys.exit(1)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        dxf_parser.close_database()


if __name__ == "__main__":
    main()
