<?php
// Database Configuration for SmartEstimate 3.0

// Database connection parameters
define('DB_HOST', 'localhost');
define('DB_NAME', 'smartestimate_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// PDO connection function
function getDatabaseConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception("Database connection failed");
    }
}

// Test database connection
function testDatabaseConnection() {
    try {
        $pdo = getDatabaseConnection();
        return true;
    } catch (Exception $e) {
        return false;
    }
}

// Panchayath/Municipality options
function getPanchayathOptions() {
    return [
        'KUMBLA GRAMA PANCHAYATH',
        'KASARAGOD MUNICIPALITY',
        'UPPALA GRAMA PANCHAYATH',
        'MANJESHWAR GRAMA PANCHAYATH',
        'BEDADKA GRAMA PANCHAYATH',
        'PULLUR PERIYA GRAMA PANCHAYATH',
        'CHENGALA GRAMA PANCHAYATH',
        'PILICODE GRAMA PANCHAYATH',
        'KARADKA GRAMA PANCHAYATH',
        'MOGRAL PUTHUR GRAMA PANCHAYATH',
        'VORKADY GRAMA PANCHAYATH',
        'CHEMNAD GRAMA PANCHAYATH',
        'TRIKARPUR GRAMA PANCHAYATH',
        'NILESHWAR GRAMA PANCHAYATH',
        'KANHANGAD MUNICIPALITY',
        'PADINHAR GRAMA PANCHAYATH',
        'MEENJA GRAMA PANCHAYATH',
        'BADIADKA GRAMA PANCHAYATH',
        'MULIYAR GRAMA PANCHAYATH',
        'DELAMPADY GRAMA PANCHAYATH',
        'BALAL GRAMA PANCHAYATH'
    ];
}
?>
