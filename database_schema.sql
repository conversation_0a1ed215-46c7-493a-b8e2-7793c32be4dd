-- SmartEstimate 3.0 Database Schema
-- MySQL Database Schema for DXF Processing and Project Management

CREATE DATABASE IF NOT EXISTS smartestimate_db;
USE smartestimate_db;

-- Projects table
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rs_no VARCHAR(50) NOT NULL,
    ward_no VARCHAR(20) NOT NULL,
    panchayath_municipality VARCHAR(100) NOT NULL,
    client_name VARCHAR(100) NOT NULL,
    dxf_filename VARCHAR(255) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_status ENUM('pending', 'processing', 'completed', 'error') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Floors table
CREATE TABLE floors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    boundary_area DECIMAL(10,2),
    min_x DECIMAL(10,3),
    min_y DECIMAL(10,3),
    max_x DECIMAL(10,3),
    max_y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Walls table
CREATE TABLE walls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    thickness DECIMAL(5,3) NOT NULL,
    area DECIMAL(10,2) NOT NULL,
    length DECIMAL(10,2) NOT NULL,
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    width DECIMAL(10,3),
    height DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Doors table
CREATE TABLE doors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    count INT DEFAULT 1,
    length DECIMAL(8,3),
    width DECIMAL(8,3),
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Windows table
CREATE TABLE windows (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    count INT DEFAULT 1,
    length DECIMAL(8,3),
    width DECIMAL(8,3),
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Openings table
CREATE TABLE openings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    count INT DEFAULT 1,
    length DECIMAL(8,3),
    width DECIMAL(8,3),
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Slabs table
CREATE TABLE slabs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    area DECIMAL(10,2) NOT NULL,
    perimeter DECIMAL(10,2),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Staircases table
CREATE TABLE staircases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    count INT DEFAULT 1,
    area DECIMAL(10,2),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Columns table
CREATE TABLE columns (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    length DECIMAL(8,3) NOT NULL,
    width DECIMAL(8,3) NOT NULL,
    count INT DEFAULT 1,
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Beams table
CREATE TABLE beams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    length DECIMAL(10,3) NOT NULL,
    width DECIMAL(8,3) NOT NULL,
    count INT DEFAULT 1,
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Flooring table
CREATE TABLE flooring (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    layer_name VARCHAR(50) NOT NULL,
    length DECIMAL(10,3),
    width DECIMAL(10,3),
    area DECIMAL(10,2),
    x DECIMAL(10,3),
    y DECIMAL(10,3),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Footprint table
CREATE TABLE footprint (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    floor_id INT,
    perimeter DECIMAL(10,2) NOT NULL,
    area DECIMAL(10,2),
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (floor_id) REFERENCES floors(id) ON DELETE SET NULL
);

-- Create indexes for better performance
CREATE INDEX idx_projects_panchayath ON projects(panchayath_municipality);
CREATE INDEX idx_projects_client ON projects(client_name);
CREATE INDEX idx_floors_project ON floors(project_id);
CREATE INDEX idx_walls_project_floor ON walls(project_id, floor_id);
CREATE INDEX idx_doors_project_floor ON doors(project_id, floor_id);
CREATE INDEX idx_windows_project_floor ON windows(project_id, floor_id);
CREATE INDEX idx_openings_project_floor ON openings(project_id, floor_id);
CREATE INDEX idx_slabs_project_floor ON slabs(project_id, floor_id);
CREATE INDEX idx_staircases_project_floor ON staircases(project_id, floor_id);
CREATE INDEX idx_columns_project_floor ON columns(project_id, floor_id);
CREATE INDEX idx_beams_project_floor ON beams(project_id, floor_id);
CREATE INDEX idx_flooring_project_floor ON flooring(project_id, floor_id);
CREATE INDEX idx_footprint_project_floor ON footprint(project_id, floor_id);
