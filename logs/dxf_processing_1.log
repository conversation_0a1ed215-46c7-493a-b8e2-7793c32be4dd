Successfully connected to MySQL database
Parsing DXF file: /Users/<USER>/Desktop/aug/SmartEstimate 3.0/uploads/dxf_files/688224ac5c516_1753359532.dxf
Available layers: ['0', 'Defpoints', 'Estimate_default', 'WALLS_20CM', 'WALLS_23CM', 'WALLS_10CM', 'GROUND_FLOOR', 'FIRST_FLOOR', 'RCC_COLUMN', 'DO<PERSON>_<PERSON>', 'DOOR_D', 'DOOR_D1', 'DOOR_D2', 'WINDOW_W1', 'WINDOW_W2', 'WINDOW_W3', 'WINDOW_W4', 'WINDOW_V1', 'STAIRCASE', 'MAIN_SLAB_TOTAL', 'MAIN_SLAB_DEDUCTION', 'RCC_LINTEL', 'RCC_BEAM', 'WINDOW_KW3', 'OPENINGS', 'FLOORING_MAIN', 'FLOORING_TOILET', 'FLOORING_KITCHEN', 'FOOTPRINT', 'KITCHEN_SLAB']
Extracted floor: GROUND_FLOOR, Area: 306.69
Extracted floor: FIRST_FLOOR, Area: 280.33
No floor boundaries found in layer: SECOND_FLOOR
No floor boundaries found in layer: THIRD_FLOOR
Extracted wall: WALLS_10CM, Length: 1.80m (longer side), Width: 0.10m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_10CM, Length: 2.00m (longer side), Width: 0.10m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_10CM, Length: 0.90m (longer side), Width: 0.10m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_10CM, Length: 1.50m (longer side), Width: 0.10m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 13.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 10.16m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 5.30m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.26m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 3.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 1.50m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 3.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 9.76m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 3.30m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.06m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 7.10m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.06m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 5.70m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 3.90m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 1.40m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 1.80m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.25m (longer side), Width: 0.19m, Orientation: horizontal, Floor: 1
Extracted wall: WALLS_20CM, Length: 1.61m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
Extracted wall: WALLS_20CM, Length: 4.40m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 2
Extracted wall: WALLS_20CM, Length: 3.90m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 5.90m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 4.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 2
Extracted wall: WALLS_20CM, Length: 4.06m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 12.80m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 2
Extracted wall: WALLS_20CM, Length: 5.66m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 4.00m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 2
Extracted wall: WALLS_20CM, Length: 3.86m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 4.16m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 3.60m (longer side), Width: 0.20m, Orientation: horizontal, Floor: 2
Extracted wall: WALLS_20CM, Length: 1.50m (longer side), Width: 0.20m, Orientation: vertical, Floor: 2
Extracted wall: WALLS_20CM, Length: 5.70m (longer side), Width: 0.20m, Orientation: vertical, Floor: 1
No walls found in layer: WALLS_23CM
No doors found in layer: DOOR_D
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D1, Length: 0.90m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D2, Length: 0.75m (longer side), Width: 0.10m, Orientation: horizontal, Position: 🏠 Inner
Extracted door: DOOR_D2, Length: 0.75m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted door: DOOR_D2, Length: 0.75m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted door: DOOR_D2, Length: 0.75m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted door: DOOR_MD, Length: 1.20m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted door: DOOR_MD, Length: 1.20m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_KW3, Length: 1.80m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted window: WINDOW_V1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted window: WINDOW_V1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_V1, Length: 0.60m (longer side), Width: 0.10m, Orientation: vertical, Position: 🏠 Inner
Extracted window: WINDOW_V1, Length: 0.75m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted window: WINDOW_W1, Length: 0.60m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted window: WINDOW_W2, Length: 1.20m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted window: WINDOW_W2, Length: 1.20m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W3, Length: 1.50m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted window: WINDOW_W3, Length: 1.50m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W3, Length: 1.75m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted window: WINDOW_W3, Length: 1.70m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted window: WINDOW_W4, Length: 1.79m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted window: WINDOW_W4, Length: 2.00m (longer side), Width: 0.20m, Orientation: horizontal, Position: ✅ Outer
Extracted opening: OPENINGS, Length: 1.58m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted opening: OPENINGS, Length: 4.02m (longer side), Width: 0.19m, Orientation: horizontal, Position: ✅ Outer
Extracted opening: OPENINGS, Length: 1.50m (longer side), Width: 0.20m, Orientation: horizontal, Position: 🏠 Inner
Extracted opening: OPENINGS, Length: 1.50m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted opening: OPENINGS, Length: 1.80m (longer side), Width: 0.20m, Orientation: vertical, Position: 🏠 Inner
Extracted opening: OPENINGS, Length: 1.60m (longer side), Width: 0.20m, Orientation: vertical, Position: ✅ Outer
Extracted slab: MAIN_SLAB_TOTAL, Area: 158.34
Extracted slab: MAIN_SLAB_TOTAL, Area: 109.06
Extracted slab: MAIN_SLAB_DEDUCTION, Area: 11.76
Extracted staircases: Floor 1, Count: 1
No columns found in layer: RCC_COLUMN
Extracted beam: Length: 4.60m (longer side), Width: 0.20m, Orientation: horizontal
Extracted beam: Length: 4.45m (longer side), Width: 0.19m, Orientation: horizontal
Extracted beam: Length: 2.00m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 2.00m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 2.03m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 2.33m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 1.90m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 4.60m (longer side), Width: 0.20m, Orientation: horizontal
Extracted beam: Length: 2.00m (longer side), Width: 0.20m, Orientation: vertical
Extracted beam: Length: 4.45m (longer side), Width: 0.22m, Orientation: horizontal
Extracted beam: Length: 2.00m (longer side), Width: 0.20m, Orientation: vertical
Extracted flooring: FLOORING_KITCHEN, Area: 11.84
Extracted flooring: FLOORING_KITCHEN, Area: 3.40
Extracted flooring: FLOORING_KITCHEN, Area: 8.00
Extracted flooring: FLOORING_MAIN, Area: 13.90
Extracted flooring: FLOORING_MAIN, Area: 12.74
Extracted flooring: FLOORING_MAIN, Area: 6.09
Extracted flooring: FLOORING_MAIN, Area: 14.26
Extracted flooring: FLOORING_MAIN, Area: 1.65
Extracted flooring: FLOORING_MAIN, Area: 23.94
Extracted flooring: FLOORING_MAIN, Area: 14.04
Extracted flooring: FLOORING_MAIN, Area: 2.70
Extracted flooring: FLOORING_MAIN, Area: 8.01
Extracted flooring: FLOORING_MAIN, Area: 13.90
Extracted flooring: FLOORING_MAIN, Area: 8.01
Extracted flooring: FLOORING_MAIN, Area: 19.30
Extracted flooring: FLOORING_MAIN, Area: 1.26
Extracted flooring: FLOORING_MAIN, Area: 11.63
Extracted flooring: FLOORING_MAIN, Area: 1.65
Extracted flooring: FLOORING_MAIN, Area: 14.26
Extracted flooring: FLOORING_TOILET, Area: 3.00
Extracted flooring: FLOORING_TOILET, Area: 3.75
Extracted flooring: FLOORING_TOILET, Area: 3.75
Extracted footprint: Perimeter: 51.92, Area: 146.19
Extracted footprint: Perimeter: 49.92, Area: 97.37

=== EXTRACTION SUMMARY ===
FLOORS: 2 items extracted
WALLS: 37 items extracted
DOORS: 13 items extracted
WINDOWS: 24 items extracted
OPENINGS: 6 items extracted
SLABS: 3 items extracted
STAIRCASES: 1 items extracted
COLUMNS: 0 items extracted
BEAMS: 11 items extracted
FLOORING: 22 items extracted
FOOTPRINT: 2 items extracted

=== LAYER ANALYSIS ===
MISSING LAYERS: SECOND_FLOOR, THIRD_FLOOR
TOTAL LAYERS FOUND: 30
EXPECTED LAYERS: 27
DXF parsing completed successfully
DXF parsing completed successfully
MySQL connection closed
