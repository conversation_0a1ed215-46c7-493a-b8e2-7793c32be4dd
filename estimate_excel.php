<?php
require_once 'config/database.php';

// Get database connection
$pdo = getDatabaseConnection();

// Get project ID from URL
$project_id = isset($_GET['project_id']) ? (int)$_GET['project_id'] : 1;

// Set headers for Excel download
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment; filename="estimate_project_' . $project_id . '.xls"');
header('Pragma: no-cache');
header('Expires: 0');

// Include the same calculation logic as estimate.php
try {
    // Get project details
    $stmt = $pdo->prepare("SELECT * FROM projects WHERE id = ?");
    $stmt->execute([$project_id]);
    $project = $stmt->fetch();

    if (!$project) {
        throw new Exception("Project not found");
    }

    // Get floors
    $stmt = $pdo->prepare("SELECT * FROM floors WHERE project_id = ? ORDER BY id");
    $stmt->execute([$project_id]);
    $floors = $stmt->fetchAll();
    $ground_floor = $floors[0] ?? null;

    // Get project settings
    $stmt = $pdo->prepare("SELECT * FROM project_settings WHERE project_id = ?");
    $stmt->execute([$project_id]);
    $settings = $stmt->fetch() ?: [];

    // Extract settings with defaults
    $excavation_depth = $settings['excavation_depth'] ?? 1.00;
    $wall_height = $settings['wall_height'] ?? 3.00;
    $basement_width = $settings['basement_width'] ?? 0.30;
    $basement_height = $settings['basement_height'] ?? 0.60;
    $lintel_thickness = $settings['lintel_thickness'] ?? 0.230;
    $main_slab_thickness = $settings['main_slab_thickness'] ?? 0.125;
    $staircase_steps = $settings['staircase_steps'] ?? 20;
    $beam_depth = $settings['beam_depth'] ?? 0.20;
    $cc_thickness = $settings['cc_thickness'] ?? 0.05;
    $slab_thickness = $settings['slab_thickness'] ?? 0.13;
    $door_height = $settings['door_height'] ?? 2.10;

    // Window heights
    $window_heights = [
        'WINDOW_KW3' => $settings['window_kw3_height'] ?? 1.10,
        'WINDOW_V1' => $settings['window_v1_height'] ?? 0.60,
        'WINDOW_W1' => $settings['window_w1_height'] ?? 1.50,
        'WINDOW_W2' => $settings['window_w2_height'] ?? 1.50,
        'WINDOW_W3' => $settings['window_w3_height'] ?? 1.50,
        'WINDOW_W4' => $settings['window_w4_height'] ?? 1.50,
    ];

    // Include all calculation functions from estimate.php
    include_once 'estimate_calculations.php';

    // Calculate all items (same as estimate.php)
    $excavation = calculateExcavation($pdo, $project_id, $ground_floor['id'], $excavation_depth);
    $foundation = calculateFoundation($pdo, $project_id, $ground_floor['id'], $basement_width, $basement_height);
    $superstructure = calculateSuperstructure($pdo, $project_id, $floors, $wall_height);
    $final_superstructure_amount = $superstructure['amount'] - $superstructure['deduction_amount'];
    $anjilywood = calculateAnjilywood($pdo, $project_id, $floors, $wall_height);
    $door_shutters = calculateDoorShutters($pdo, $project_id, $floors);
    $ms_grills = calculateMSGrills($pdo, $project_id, $floors);
    $rcc_work = calculateRCCWork($pdo, $project_id, $floors, $lintel_thickness, $main_slab_thickness, $wall_height, $staircase_steps, $beam_depth);
    $cc_work = calculateCCWork($pdo, $project_id, $ground_floor['id'], $cc_thickness);
    $reinforcement_work = calculateReinforcementWork($pdo, $project_id, $rcc_work['total_qty_m3'], $settings['reinforcement'] ?? 110.00);
    $plastering_work = calculatePlasteringWork($pdo, $project_id, $floors, $rcc_work['items']);
    $wall_plastering_work = calculateWallPlasteringWork($pdo, $project_id, $floors, $wall_height, $basement_height, $slab_thickness, $window_heights, $door_height);
    $electrical_work = calculateElectricalWork($pdo, $project_id, $settings['electrical_works'] ?? 100000.00);
    $granite_flooring_work = calculateGraniteFlooringWork($pdo, $project_id);
    $main_flooring_work = calculateMainFlooringWork($pdo, $project_id, $floors);
    $wall_dado_work = calculateWallDadoWork($pdo, $project_id, $floors, $door_height);
    $painting_work = calculatePaintingWork($pdo, $project_id, $plastering_work['total_qty_m2'], $wall_plastering_work['total_qty_m2']);
    $plumbing_work = calculatePlumbingWork($pdo, $project_id, $settings['plumbing_work'] ?? 75000.00);
    $unforeseen_work = calculateUnforeseenWork($pdo, $project_id, $settings['unforeseen_items'] ?? 50000.00);

    // Calculate grand total
    $grand_total = $excavation['amount'] + $foundation['amount'] + $final_superstructure_amount + $anjilywood['amount'] + $door_shutters['amount'] + $ms_grills['amount'] + $rcc_work['amount'] + $cc_work['amount'] + $reinforcement_work['amount'] + $plastering_work['amount'] + $wall_plastering_work['amount'] + $electrical_work['amount'] + $granite_flooring_work['amount'] + $main_flooring_work['amount'] + $wall_dado_work['amount'] + $painting_work['amount'] + $plumbing_work['amount'] + $unforeseen_work['amount'];

} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
    exit;
}

// Include number to words function
function numberToWords($number) {
    $ones = array(
        0 => '', 1 => 'One', 2 => 'Two', 3 => 'Three', 4 => 'Four', 5 => 'Five',
        6 => 'Six', 7 => 'Seven', 8 => 'Eight', 9 => 'Nine', 10 => 'Ten',
        11 => 'Eleven', 12 => 'Twelve', 13 => 'Thirteen', 14 => 'Fourteen', 15 => 'Fifteen',
        16 => 'Sixteen', 17 => 'Seventeen', 18 => 'Eighteen', 19 => 'Nineteen'
    );
    
    $tens = array(
        0 => '', 2 => 'Twenty', 3 => 'Thirty', 4 => 'Forty', 5 => 'Fifty',
        6 => 'Sixty', 7 => 'Seventy', 8 => 'Eighty', 9 => 'Ninety'
    );
    
    if ($number == 0) return 'Zero';
    
    $words = '';
    
    // Handle crores
    if ($number >= 10000000) {
        $crores = intval($number / 10000000);
        $words .= convertHundreds($crores, $ones, $tens) . ' Crore ';
        $number %= 10000000;
    }
    
    // Handle lakhs
    if ($number >= 100000) {
        $lakhs = intval($number / 100000);
        $words .= convertHundreds($lakhs, $ones, $tens) . ' Lakh ';
        $number %= 100000;
    }
    
    // Handle thousands
    if ($number >= 1000) {
        $thousands = intval($number / 1000);
        $words .= convertHundreds($thousands, $ones, $tens) . ' Thousand ';
        $number %= 1000;
    }
    
    // Handle hundreds
    if ($number >= 100) {
        $hundreds = intval($number / 100);
        $words .= $ones[$hundreds] . ' Hundred ';
        $number %= 100;
    }
    
    // Handle remaining numbers
    if ($number > 0) {
        if ($number < 20) {
            $words .= $ones[$number];
        } else {
            $words .= $tens[intval($number / 10)];
            if ($number % 10 > 0) {
                $words .= ' ' . $ones[$number % 10];
            }
        }
    }
    
    return trim($words);
}

function convertHundreds($number, $ones, $tens) {
    $words = '';
    
    if ($number >= 100) {
        $words .= $ones[intval($number / 100)] . ' Hundred ';
        $number %= 100;
    }
    
    if ($number > 0) {
        if ($number < 20) {
            $words .= $ones[$number];
        } else {
            $words .= $tens[intval($number / 10)];
            if ($number % 10 > 0) {
                $words .= ' ' . $ones[$number % 10];
            }
        }
    }
    
    return trim($words);
}

$grand_total_words = numberToWords(intval($grand_total));
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Construction Estimate - Project <?php echo htmlspecialchars($project['project_name']); ?></title>
</head>
<body>
    <table border="1" cellpadding="5" cellspacing="0" style="width: 100%; border-collapse: collapse;">
        <thead>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <th colspan="8" style="text-align: center; padding: 15px; font-size: 18px;">
                    CONSTRUCTION ESTIMATE - <?php echo strtoupper(htmlspecialchars($project['project_name'])); ?>
                </th>
            </tr>
            <tr style="background-color: #e9ecef; font-weight: bold;">
                <th>Sl.No</th>
                <th>Description</th>
                <th>No</th>
                <th>L</th>
                <th>B</th>
                <th>D</th>
                <th>Qty</th>
                <th>Amount (₹)</th>
            </tr>
        </thead>
        <tbody>
            <!-- 1. Excavation -->
            <tr style="background-color: #d4edda;">
                <td colspan="8" style="font-weight: bold;">1. EXCAVATION</td>
            </tr>
            <tr>
                <td></td>
                <td>Excavation for foundation including cost of all materials and labour charges. Etc. complete<br><strong><?php echo $excavation['description_summary']; ?></strong></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td style="text-align: right;">₹<?php echo number_format($excavation['amount'], 2); ?></td>
            </tr>
            <?php foreach ($excavation['items'] as $item): ?>
            <tr>
                <td><?php echo $item['sl_no']; ?></td>
                <td><?php echo htmlspecialchars($item['description']); ?></td>
                <td><?php echo $item['no']; ?></td>
                <td><?php echo $item['l']; ?></td>
                <td><?php echo $item['b']; ?></td>
                <td><?php echo $item['d']; ?></td>
                <td><?php echo $item['qty']; ?></td>
                <td></td>
            </tr>
            <?php endforeach; ?>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <td colspan="6">Total Excavation</td>
                <td><?php echo number_format($excavation['total_qty_m3'], 3); ?> M³</td>
                <td style="text-align: right;">₹<?php echo number_format($excavation['amount'], 2); ?></td>
            </tr>

            <!-- Add more sections here... -->

            <!-- Grand Total -->
            <tr style="background-color: #343a40; color: white; font-weight: bold;">
                <td colspan="7">GRAND TOTAL</td>
                <td style="text-align: right;">₹<?php echo number_format($grand_total, 2); ?></td>
            </tr>
            <tr style="background-color: #f8f9fa; font-weight: bold;">
                <td colspan="8" style="text-align: center; padding: 15px;">
                    Amount in Words: <?php echo $grand_total_words; ?> Rupees Only
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>
