# SmartEstimate 3.0 - Apache Configuration

# Enable rewrite engine
RewriteEngine On

# Security: Prevent access to sensitive files
<Files "*.sql">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.py">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "requirements.txt">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "setup.sh">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Prevent access to config directory from web
<Directory "config">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

# Set default index file
DirectoryIndex index.php

# PHP settings for file uploads
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Cache control for static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>
