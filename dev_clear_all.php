<?php
/**
 * Development Utility: Clear All Projects
 * WARNING: This will permanently delete ALL projects and associated data!
 * Use only in development environment.
 */

require_once 'config/database.php';

// Security check - only allow in development
$is_development = true; // Set to false in production

if (!$is_development) {
    die('This utility is disabled in production environment.');
}

$confirm = $_GET['confirm'] ?? '';
$action_performed = false;
$error_message = '';

if ($confirm === 'yes') {
    $pdo = null;
    try {
        $pdo = getDatabaseConnection();
        $pdo->beginTransaction();
        
        // Get all DXF filenames before deletion
        $stmt = $pdo->query("SELECT dxf_filename FROM projects");
        $dxf_files = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Clear all tables in correct order
        $tables_to_clear = [
            'footprint', 'flooring', 'beams', 'columns', 'staircases', 
            'slabs', 'openings', 'windows', 'doors', 'walls', 'floors', 'projects'
        ];
        
        foreach ($tables_to_clear as $table) {
            try {
                $pdo->exec("DELETE FROM {$table}");
                // Reset auto-increment
                $pdo->exec("ALTER TABLE {$table} AUTO_INCREMENT = 1");
            } catch (PDOException $e) {
                // Continue with other tables if one fails
                error_log("Failed to clear table {$table}: " . $e->getMessage());
            }
        }
        
        // Delete all DXF files
        $deleted_files = 0;
        foreach ($dxf_files as $filename) {
            $file_path = __DIR__ . '/uploads/dxf_files/' . $filename;
            if (file_exists($file_path)) {
                unlink($file_path);
                $deleted_files++;
            }
        }
        
        // Delete all log files
        $log_dir = __DIR__ . '/logs/';
        if (is_dir($log_dir)) {
            $log_files = glob($log_dir . 'dxf_processing_*.log');
            foreach ($log_files as $log_file) {
                unlink($log_file);
            }
        }
        
        $pdo->commit();
        $action_performed = true;
        
    } catch (Exception $e) {
        if ($pdo && $pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error_message = "Error: " . $e->getMessage();
    }
}

// Get current statistics
try {
    $pdo = getDatabaseConnection();
    
    $stats = [];
    $tables_to_count = [
        'projects', 'floors', 'walls', 'doors', 'windows', 
        'openings', 'slabs', 'staircases', 'columns', 'beams', 
        'flooring', 'footprint'
    ];
    
    foreach ($tables_to_count as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM {$table}");
        $stats[$table] = $stmt->fetchColumn();
    }
    
    // Count DXF files
    $dxf_dir = __DIR__ . '/uploads/dxf_files/';
    $dxf_files_count = 0;
    if (is_dir($dxf_dir)) {
        $dxf_files_count = count(glob($dxf_dir . '*.dxf'));
    }
    
} catch (Exception $e) {
    $error_message = "Error getting statistics: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development: Clear All Projects - SmartEstimate 3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #dc3545, #c82333);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 30px rgba(0,0,0,0.3);
        }
        
        h1 {
            color: #dc3545;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 10px;
        }
        
        .dev-warning {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
            border: 1px solid #ffeaa7;
        }
        
        .warning h3 {
            margin-bottom: 10px;
            color: #dc3545;
        }
        
        .stats-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        
        .stats-section h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #ddd;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #dc3545;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-block;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220,53,69,0.4);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            text-align: center;
            font-weight: bold;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dev-warning">
            🚨 DEVELOPMENT UTILITY 🚨
        </div>
        
        <h1>Clear All Projects</h1>
        
        <?php if ($error_message): ?>
            <div class="error">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action_performed): ?>
            <div class="success">
                ✅ All projects and associated data have been successfully cleared!<br>
                Database has been reset and all files have been deleted.
            </div>
        <?php else: ?>
            <div class="warning">
                <h3>⚠️ DANGER: This action will permanently delete ALL data!</h3>
                <p>This development utility will completely clear:</p>
                <ul style="margin-top: 10px; margin-left: 20px;">
                    <li>All projects and their information</li>
                    <li>All extracted DXF data (floors, walls, doors, windows, etc.)</li>
                    <li>All uploaded DXF files from the filesystem</li>
                    <li>All processing log files</li>
                    <li>Reset all auto-increment counters</li>
                </ul>
                <p style="margin-top: 15px; font-weight: bold; color: #dc3545;">
                    This action cannot be undone!
                </p>
            </div>
        <?php endif; ?>
        
        <!-- Current Statistics -->
        <?php if (isset($stats)): ?>
            <div class="stats-section">
                <h3>Current Database Statistics</h3>
                <div class="stats-grid">
                    <?php foreach ($stats as $table => $count): ?>
                        <div class="stat-item">
                            <div class="stat-number"><?php echo $count; ?></div>
                            <div class="stat-label"><?php echo ucfirst(str_replace('_', ' ', $table)); ?></div>
                        </div>
                    <?php endforeach; ?>
                    <div class="stat-item">
                        <div class="stat-number"><?php echo $dxf_files_count ?? 0; ?></div>
                        <div class="stat-label">DXF Files</div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (!$action_performed): ?>
            <div class="action-buttons">
                <a href="dev_clear_all.php?confirm=yes" class="btn btn-danger">
                    🗑️ Clear All Projects
                </a>
                <a href="index.php" class="btn btn-secondary">Cancel</a>
            </div>
        <?php endif; ?>
        
        <div class="nav-links">
            <a href="index.php">← Back to Dashboard</a>
            <a href="view_projects.php">View Projects</a>
            <a href="add_project.php">Add Project</a>
        </div>
    </div>
</body>
</html>
