<?php
require_once 'config/database.php';

// Initialize variables
$errors = [];
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form data
    $rs_no = trim($_POST['rs_no'] ?? '');
    $ward_no = trim($_POST['ward_no'] ?? '');
    $panchayath_municipality = trim($_POST['panchayath_municipality'] ?? '');
    $client_name = trim($_POST['client_name'] ?? '');
    
    // Validation
    if (empty($rs_no)) {
        $errors[] = "R.S. No is required";
    }
    if (empty($ward_no)) {
        $errors[] = "Ward No is required";
    }
    if (empty($panchayath_municipality)) {
        $errors[] = "Panchayath/Municipality is required";
    }
    if (empty($client_name)) {
        $errors[] = "Client Name is required";
    }
    
    // Validate file upload
    if (!isset($_FILES['dxf_file']) || $_FILES['dxf_file']['error'] !== UPLOAD_ERR_OK) {
        $errors[] = "DXF file is required";
    } else {
        $file = $_FILES['dxf_file'];
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if ($file_extension !== 'dxf') {
            $errors[] = "Only DXF files are allowed";
        }
        
        if ($file['size'] > 50 * 1024 * 1024) { // 50MB limit
            $errors[] = "File size must be less than 50MB";
        }
    }
    
    // If no errors, process the form
    if (empty($errors)) {
        try {
            $pdo = getDatabaseConnection();
            
            // Create uploads directory if it doesn't exist
            $upload_dir = 'uploads/dxf_files/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // Generate unique filename
            $file_extension = strtolower(pathinfo($_FILES['dxf_file']['name'], PATHINFO_EXTENSION));
            $unique_filename = uniqid() . '_' . time() . '.' . $file_extension;
            $upload_path = $upload_dir . $unique_filename;
            
            // Move uploaded file
            if (move_uploaded_file($_FILES['dxf_file']['tmp_name'], $upload_path)) {
                // Insert project into database
                $stmt = $pdo->prepare("
                    INSERT INTO projects (rs_no, ward_no, panchayath_municipality, client_name, dxf_filename, processing_status)
                    VALUES (?, ?, ?, ?, ?, 'pending')
                ");
                
                $stmt->execute([$rs_no, $ward_no, $panchayath_municipality, $client_name, $unique_filename]);
                $project_id = $pdo->lastInsertId();
                
                // Call Python script to process DXF file
                $python_script = __DIR__ . '/dxf_parser.py';
                $full_upload_path = __DIR__ . '/' . $upload_path;
                
                // Build command
                $command = sprintf(
                    'python3 %s %s %d --host=%s --database=%s --user=%s --password=%s 2>&1',
                    escapeshellarg($python_script),
                    escapeshellarg($full_upload_path),
                    $project_id,
                    escapeshellarg(DB_HOST),
                    escapeshellarg(DB_NAME),
                    escapeshellarg(DB_USER),
                    escapeshellarg(DB_PASS)
                );
                
                // Update project status to processing
                $stmt = $pdo->prepare("UPDATE projects SET processing_status = 'processing' WHERE id = ?");
                $stmt->execute([$project_id]);
                
                // Execute Python script in background
                exec($command . ' > /dev/null 2>&1 &');
                
                $success_message = "Project created successfully! DXF file is being processed. Project ID: " . $project_id;
                
                // Clear form data
                $rs_no = $ward_no = $panchayath_municipality = $client_name = '';
                
            } else {
                $errors[] = "Failed to upload file";
            }
            
        } catch (Exception $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

// Get Panchayath options
$panchayath_options = getPanchayathOptions();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Project - SmartEstimate 3.0</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"],
        select,
        input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus,
        select:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .file-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            font-weight: bold;
        }
        
        .nav-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Add New Project</h1>
        
        <?php if (!empty($errors)): ?>
            <div class="error">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="success">
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="rs_no">R.S. No *</label>
                <input type="text" id="rs_no" name="rs_no" value="<?php echo htmlspecialchars($rs_no ?? ''); ?>" 
                       placeholder="e.g., 116/3A1A2PT3" required>
            </div>
            
            <div class="form-group">
                <label for="ward_no">Ward No *</label>
                <input type="text" id="ward_no" name="ward_no" value="<?php echo htmlspecialchars($ward_no ?? ''); ?>" 
                       placeholder="e.g., 3" required>
            </div>
            
            <div class="form-group">
                <label for="panchayath_municipality">Panchayath/Municipality *</label>
                <select id="panchayath_municipality" name="panchayath_municipality" required>
                    <option value="">Select Panchayath/Municipality</option>
                    <?php foreach ($panchayath_options as $option): ?>
                        <option value="<?php echo htmlspecialchars($option); ?>" 
                                <?php echo (isset($panchayath_municipality) && $panchayath_municipality === $option) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($option); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="client_name">Client Name *</label>
                <input type="text" id="client_name" name="client_name" value="<?php echo htmlspecialchars($client_name ?? ''); ?>" 
                       placeholder="e.g., JOHN DOE" required>
            </div>
            
            <div class="form-group">
                <label for="dxf_file">DXF File *</label>
                <input type="file" id="dxf_file" name="dxf_file" accept=".dxf" required>
                <div class="file-info">Only DXF files are allowed. Maximum file size: 50MB</div>
            </div>
            
            <button type="submit" class="btn">Create Project</button>
        </form>
        
        <div class="nav-links">
            <a href="view_projects.php">View All Projects</a>
        </div>
    </div>
</body>
</html>
